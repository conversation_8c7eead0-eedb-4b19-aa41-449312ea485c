import React from 'react';
import { BottomNavigation, TabType } from '@/components/navigation/BottomNavigation';
import { PlayerTab } from '@/components/tabs/PlayerTab';
import { StatisticsTab } from '@/components/tabs/StatisticsTab';
import { ProfileTab } from '@/components/tabs/ProfileTab';
import { SearchTab } from '@/components/tabs/SearchTab';
import { cn } from '@/lib/utils';

interface MobileLayoutProps {
  children?: React.ReactNode;
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  session: any;
  isHost: boolean;
  isGuest: boolean;
  onVote?: (trackId: string, emoji: string, value: number) => void;
  onAddTrack?: (track: any) => void;
  onRemoveTrack?: (trackId: string) => void;
  onPinTrack?: (trackId: string) => void;
  onPlayNext?: () => void;
  onPlayPrevious?: () => void;
}

export function MobileLayout({
  children,
  activeTab,
  onTabChange,
  session,
  isHost,
  isGuest,
  onVote,
  onAddTrack,
  onRemoveTrack,
  onPinTrack,
  onPlayNext,
  onPlayPrevious
}: MobileLayoutProps) {
  const renderTabContent = () => {
    switch (activeTab) {
      case 'player':
        return (
          <PlayerTab
            session={session}
            isHost={isHost}
            isGuest={isGuest}
            onVote={onVote}
            onRemoveTrack={onRemoveTrack}
            onPinTrack={onPinTrack}
            onPlayNext={onPlayNext}
            onPlayPrevious={onPlayPrevious}
          />
        );
      case 'statistics':
        return (
          <StatisticsTab
            session={session}
            isHost={isHost}
            isGuest={isGuest}
          />
        );
      case 'profile':
        return (
          <ProfileTab
            session={session}
            isHost={isHost}
            isGuest={isGuest}
          />
        );
      case 'search':
        return (
          <SearchTab
            session={session}
            isHost={isHost}
            isGuest={isGuest}
            onAddTrack={onAddTrack}
          />
        );
      default:
        return children;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white flex flex-col">
      {/* Main content area with bottom padding for navigation */}
      <main 
        className={cn(
          "flex-1 overflow-y-auto",
          "pb-20", // Space for bottom navigation
          "safe-area-top" // Handle notch on iOS
        )}
      >
        <div className="h-full">
          {renderTabContent()}
        </div>
      </main>

      {/* Bottom Navigation */}
      <BottomNavigation
        activeTab={activeTab}
        onTabChange={onTabChange}
      />
    </div>
  );
}
