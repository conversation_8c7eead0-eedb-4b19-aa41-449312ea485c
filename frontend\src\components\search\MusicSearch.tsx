import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Plus, Loader2, Music } from 'lucide-react';
import { cn } from '@/lib/utils';
import debounce from 'lodash/debounce';

interface MusicSearchProps {
  onSearch: (query: string) => void;
  searchResults: any[];
  isSearching: boolean;
  onAddTrack: (track: any) => void;
  variant: 'mobile' | 'desktop';
}

export function MusicSearch({
  onSearch,
  searchResults,
  isSearching,
  onAddTrack,
  variant
}: MusicSearchProps) {
  const [query, setQuery] = useState('');
  const isMobile = variant === 'mobile';

  // Debounced search function
  const debouncedSearch = debounce((searchQuery: string) => {
    onSearch(searchQuery);
  }, 300);

  useEffect(() => {
    debouncedSearch(query);
    return () => {
      debouncedSearch.cancel();
    };
  }, [query, debouncedSearch]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <Input
          type="text"
          placeholder="Search for songs, artists, or albums..."
          value={query}
          onChange={handleInputChange}
          className={cn(
            "pl-10 pr-4 bg-gray-800 border-gray-600 text-white placeholder-gray-400",
            "focus:border-purple-500 focus:ring-purple-500",
            isMobile ? "h-12 text-base" : "h-10"
          )}
        />
        {isSearching && (
          <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-purple-400 animate-spin" />
        )}
      </div>

      {/* Search Results */}
      {query && (
        <div className="space-y-2">
          {isSearching ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 text-purple-400 animate-spin mx-auto mb-2" />
              <p className="text-gray-400">Searching...</p>
            </div>
          ) : searchResults.length > 0 ? (
            <div className="space-y-2">
              <h4 className="text-sm font-semibold text-gray-400 uppercase tracking-wide">
                Search Results
              </h4>
              {searchResults.map((track) => (
                <SearchResultCard
                  key={track.id}
                  track={track}
                  onAdd={() => onAddTrack(track)}
                  variant={variant}
                />
              ))}
            </div>
          ) : query.length > 2 ? (
            <div className="text-center py-8">
              <Music className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-400">No results found</p>
              <p className="text-gray-500 text-sm">Try different keywords</p>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}

interface SearchResultCardProps {
  track: any;
  onAdd: () => void;
  variant: 'mobile' | 'desktop';
}

function SearchResultCard({ track, onAdd, variant }: SearchResultCardProps) {
  const isMobile = variant === 'mobile';

  return (
    <div className={cn(
      "bg-gray-800/50 rounded-lg border border-gray-700/50 p-3",
      "transition-all duration-200 hover:bg-gray-700/50"
    )}>
      <div className="flex items-center space-x-3">
        {/* Album Art */}
        <div className={cn(
          "flex-shrink-0 rounded-md overflow-hidden",
          isMobile ? "w-12 h-12" : "w-16 h-16"
        )}>
          <img
            src={track.album?.images?.[0]?.url || '/placeholder-album.png'}
            alt={`${track.name} album art`}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Track Info */}
        <div className="flex-1 min-w-0">
          <h4 className={cn(
            "font-semibold text-white truncate",
            isMobile ? "text-sm" : "text-base"
          )}>
            {track.name}
          </h4>
          <p className={cn(
            "text-gray-400 truncate",
            isMobile ? "text-xs" : "text-sm"
          )}>
            {track.artists?.map((artist: any) => artist.name).join(', ')}
          </p>
          <div className="flex items-center space-x-2 mt-1">
            <span className="text-xs text-gray-500">
              {track.album?.name}
            </span>
            <span className="text-xs text-gray-500">•</span>
            <span className="text-xs text-gray-500">
              {formatDuration(track.duration_ms)}
            </span>
          </div>
        </div>

        {/* Add Button */}
        <Button
          onClick={onAdd}
          size={isMobile ? "sm" : "default"}
          className="bg-purple-600 hover:bg-purple-700 text-white flex-shrink-0"
        >
          <Plus className="h-4 w-4 mr-1" />
          Add
        </Button>
      </div>
    </div>
  );
}
