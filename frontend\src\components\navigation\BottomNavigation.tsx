import React from 'react';
import { Music, BarChart3, User, Search } from 'lucide-react';
import { cn } from '@/lib/utils';

export type TabType = 'player' | 'statistics' | 'profile' | 'search';

interface BottomNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  className?: string;
}

interface NavItem {
  id: TabType;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  badge?: number;
}

const navItems: NavItem[] = [
  {
    id: 'player',
    icon: Music,
    label: 'Player',
  },
  {
    id: 'statistics',
    icon: BarChart3,
    label: 'Stats',
  },
  {
    id: 'profile',
    icon: User,
    label: 'Profile',
  },
  {
    id: 'search',
    icon: Search,
    label: 'Search',
  },
];

export function BottomNavigation({ 
  activeTab, 
  onTabChange, 
  className 
}: BottomNavigationProps) {
  return (
    <nav 
      className={cn(
        "fixed bottom-0 left-0 right-0 z-50",
        "bg-gray-900/95 backdrop-blur-lg border-t border-gray-700",
        "md:hidden", // Hide on desktop
        className
      )}
    >
      <div className="flex items-center justify-around px-2 py-2 safe-area-pb">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeTab === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className={cn(
                "flex flex-col items-center justify-center",
                "min-w-0 flex-1 px-2 py-2 rounded-lg",
                "transition-all duration-200 ease-in-out",
                "touch-manipulation", // Optimize for touch
                isActive 
                  ? "bg-purple-600/20 text-purple-400" 
                  : "text-gray-400 hover:text-gray-300 hover:bg-gray-800/50"
              )}
              aria-label={item.label}
              role="tab"
              aria-selected={isActive}
            >
              <div className="relative">
                <Icon 
                  className={cn(
                    "h-6 w-6 transition-transform duration-200",
                    isActive && "scale-110"
                  )} 
                />
                {item.badge && item.badge > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </div>
              <span 
                className={cn(
                  "text-xs mt-1 font-medium transition-all duration-200",
                  isActive ? "text-purple-400" : "text-gray-500"
                )}
              >
                {item.label}
              </span>
            </button>
          );
        })}
      </div>
    </nav>
  );
}
