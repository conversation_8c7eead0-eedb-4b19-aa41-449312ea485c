import React from 'react';
import { Button } from '@/components/ui/button';
import { VOTE_EMOJIS } from '@/constants/vote-emojis';
import { cn } from '@/lib/utils';

interface VoteButtonsProps {
  onVote: (emoji: string, value: number) => void;
  userVote?: { emoji: string; value: number } | null;
  variant?: 'normal' | 'large' | 'compact';
  className?: string;
}

export function VoteButtons({ 
  onVote, 
  userVote, 
  variant = 'normal',
  className 
}: VoteButtonsProps) {
  const isLarge = variant === 'large';
  const isCompact = variant === 'compact';

  return (
    <div className={cn("space-y-3", className)}>
      <h4 className={cn(
        "font-bold text-center text-white",
        isLarge ? "text-lg" : "text-base"
      )}>
        ⭐ Rate this track
      </h4>
      
      <div className={cn(
        "flex items-center justify-center",
        isLarge ? "space-x-4" : "space-x-3"
      )}>
        {VOTE_EMOJIS.map(({ emoji, value, icon: Icon, color, label }) => {
          const isUserVote = userVote?.emoji === emoji;
          const buttonSize = isLarge ? "lg" : isCompact ? "sm" : "default";
          
          return (
            <Button
              key={emoji}
              size={buttonSize}
              variant={isUserVote ? "default" : "ghost"}
              onClick={() => onVote(emoji, value)}
              className={cn(
                "transition-all duration-200 transform hover:scale-110",
                "touch-manipulation", // Optimize for touch
                isUserVote 
                  ? "ring-2 ring-purple-400 bg-purple-600/20 text-purple-400" 
                  : `${color} hover:bg-gray-700/50`,
                isLarge && "h-14 w-14",
                isCompact && "h-8 w-8"
              )}
              title={isUserVote 
                ? `Your vote: ${label} (${value > 0 ? '+' : ''}${value} pts)` 
                : `${label}: ${value > 0 ? '+' : ''}${value} points`
              }
            >
              <Icon className={cn(
                isLarge ? "h-6 w-6" : isCompact ? "h-3 w-3" : "h-5 w-5"
              )} />
            </Button>
          );
        })}
      </div>
      
      {userVote && (
        <div className="text-center">
          <span className="text-xs text-gray-400">
            You voted: {VOTE_EMOJIS.find(e => e.emoji === userVote.emoji)?.label} 
            ({userVote.value > 0 ? '+' : ''}{userVote.value} pts)
          </span>
        </div>
      )}
    </div>
  );
}

interface SingleVoteButtonProps {
  emoji: string;
  value: number;
  isSelected?: boolean;
  onClick: () => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function SingleVoteButton({
  emoji,
  value,
  isSelected = false,
  onClick,
  size = 'md',
  className
}: SingleVoteButtonProps) {
  const emojiConfig = VOTE_EMOJIS.find(e => e.emoji === emoji);
  if (!emojiConfig) return null;

  const Icon = emojiConfig.icon;
  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-10 w-10", 
    lg: "h-12 w-12"
  };

  return (
    <Button
      size="icon"
      variant={isSelected ? "default" : "ghost"}
      onClick={onClick}
      className={cn(
        sizeClasses[size],
        "transition-all duration-200 transform hover:scale-110",
        "touch-manipulation",
        isSelected 
          ? "ring-2 ring-purple-400 bg-purple-600/20 text-purple-400" 
          : `${emojiConfig.color} hover:bg-gray-700/50`,
        className
      )}
      title={`${emojiConfig.label}: ${value > 0 ? '+' : ''}${value} points`}
    >
      <Icon className="h-5 w-5" />
    </Button>
  );
}
