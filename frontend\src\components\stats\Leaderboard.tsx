import React from 'react';
import { Trophy, Crown, Medal, Award } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LeaderboardProps {
  session: any;
  variant: 'mobile' | 'desktop';
}

export function Leaderboard({ session, variant }: LeaderboardProps) {
  const isMobile = variant === 'mobile';

  // Sort participants by points
  const leaderboard = (session?.participants || [])
    .sort((a: any, b: any) => b.points - a.points)
    .slice(0, 10); // Top 10

  if (leaderboard.length === 0) {
    return (
      <div className="text-center py-8">
        <Trophy className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-400">No participants yet</p>
      </div>
    );
  }

  const getRankIcon = (position: number) => {
    switch (position) {
      case 1:
        return Crown;
      case 2:
        return Trophy;
      case 3:
        return Medal;
      default:
        return Award;
    }
  };

  const getRankColor = (position: number) => {
    switch (position) {
      case 1:
        return 'text-yellow-400';
      case 2:
        return 'text-gray-300';
      case 3:
        return 'text-orange-400';
      default:
        return 'text-gray-500';
    }
  };

  const getRankBg = (position: number) => {
    switch (position) {
      case 1:
        return 'bg-yellow-900/20 border-yellow-700/30';
      case 2:
        return 'bg-gray-700/20 border-gray-600/30';
      case 3:
        return 'bg-orange-900/20 border-orange-700/30';
      default:
        return 'bg-gray-800/50 border-gray-700/30';
    }
  };

  return (
    <div className="space-y-2">
      {leaderboard.map((participant: any, index: number) => {
        const position = index + 1;
        const Icon = getRankIcon(position);
        const rankColor = getRankColor(position);
        const rankBg = getRankBg(position);

        return (
          <div
            key={participant.id}
            className={cn(
              "flex items-center space-x-3 p-3 rounded-lg border",
              rankBg,
              "transition-all duration-200 hover:scale-[1.02]"
            )}
          >
            {/* Rank */}
            <div className={cn(
              "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
              position <= 3 ? "bg-gray-900/50" : "bg-gray-700/50"
            )}>
              {position <= 3 ? (
                <Icon className={cn("h-4 w-4", rankColor)} />
              ) : (
                <span className={cn("text-sm font-bold", rankColor)}>
                  {position}
                </span>
              )}
            </div>

            {/* Avatar */}
            <div className={cn(
              "flex-shrink-0 rounded-full overflow-hidden",
              isMobile ? "w-8 h-8" : "w-10 h-10"
            )}>
              {participant.user?.avatarUrl ? (
                <img
                  src={participant.user.avatarUrl}
                  alt={participant.user.displayName}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-purple-600 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">
                    {(participant.user?.displayName || participant.name)?.[0]?.toUpperCase()}
                  </span>
                </div>
              )}
            </div>

            {/* Name */}
            <div className="flex-1 min-w-0">
              <p className={cn(
                "font-semibold text-white truncate",
                isMobile ? "text-sm" : "text-base"
              )}>
                {participant.user?.displayName || participant.name}
              </p>
              {participant.user?.level && (
                <p className="text-xs text-gray-400">
                  Level {participant.user.level}
                </p>
              )}
            </div>

            {/* Points */}
            <div className="flex-shrink-0 text-right">
              <p className={cn(
                "font-bold",
                position === 1 ? "text-yellow-400" : "text-white",
                isMobile ? "text-sm" : "text-base"
              )}>
                {participant.points}
              </p>
              <p className="text-xs text-gray-400">points</p>
            </div>

            {/* Badge for top 3 */}
            {position <= 3 && (
              <div className={cn(
                "flex-shrink-0 px-2 py-1 rounded-full text-xs font-bold",
                position === 1 ? "bg-yellow-400/20 text-yellow-400" :
                position === 2 ? "bg-gray-400/20 text-gray-300" :
                "bg-orange-400/20 text-orange-400"
              )}>
                #{position}
              </div>
            )}
          </div>
        );
      })}

      {/* Show more indicator */}
      {session?.participants?.length > 10 && (
        <div className="text-center py-2">
          <span className="text-xs text-gray-500">
            And {session.participants.length - 10} more participants...
          </span>
        </div>
      )}
    </div>
  );
}
