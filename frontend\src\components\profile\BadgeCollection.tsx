import React from 'react';
import { Award, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BadgeCollectionProps {
  user: any;
  variant: 'mobile' | 'desktop';
}

// Mock badges data - in real app this would come from the backend
const availableBadges = [
  {
    id: 'first_track',
    name: 'First Track',
    description: 'Added your first track to a session',
    icon: '🎵',
    rarity: 'common',
  },
  {
    id: 'vote_master',
    name: 'Vote Master',
    description: 'Cast 50 votes',
    icon: '🗳️',
    rarity: 'uncommon',
  },
  {
    id: 'party_starter',
    name: 'Party Starter',
    description: 'Host 5 sessions',
    icon: '🎉',
    rarity: 'rare',
  },
  {
    id: 'music_guru',
    name: '<PERSON> Guru',
    description: 'Have 10 tracks reach the top of the queue',
    icon: '🎼',
    rarity: 'epic',
  },
  {
    id: 'social_butterfly',
    name: 'Social Butterfly',
    description: 'Join 20 different sessions',
    icon: '🦋',
    rarity: 'rare',
  },
  {
    id: 'perfectionist',
    name: 'Perfectionist',
    description: 'Get 100% positive votes on 5 tracks',
    icon: '⭐',
    rarity: 'legendary',
  },
];

export function BadgeCollection({ user, variant }: BadgeCollectionProps) {
  const isMobile = variant === 'mobile';
  
  // Mock user badges - in real app this would come from user data
  const userBadges = user?.badges || ['first_track', 'vote_master'];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'border-gray-500 bg-gray-500/10';
      case 'uncommon':
        return 'border-green-500 bg-green-500/10';
      case 'rare':
        return 'border-blue-500 bg-blue-500/10';
      case 'epic':
        return 'border-purple-500 bg-purple-500/10';
      case 'legendary':
        return 'border-yellow-500 bg-yellow-500/10';
      default:
        return 'border-gray-500 bg-gray-500/10';
    }
  };

  return (
    <div className="space-y-4">
      {/* Progress */}
      <div className="bg-gray-700/30 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-400">Badge Progress</span>
          <span className="text-sm font-semibold text-white">
            {userBadges.length}/{availableBadges.length}
          </span>
        </div>
        <div className="w-full bg-gray-600 rounded-full h-2">
          <div
            className="bg-purple-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(userBadges.length / availableBadges.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Badge Grid */}
      <div className={cn(
        "grid gap-3",
        isMobile ? "grid-cols-2" : "grid-cols-3"
      )}>
        {availableBadges.map((badge) => {
          const isUnlocked = userBadges.includes(badge.id);
          const rarityColor = getRarityColor(badge.rarity);

          return (
            <div
              key={badge.id}
              className={cn(
                "relative rounded-lg border p-3 text-center transition-all duration-200",
                isUnlocked 
                  ? `${rarityColor} hover:scale-105` 
                  : "border-gray-700 bg-gray-800/50 opacity-60"
              )}
            >
              {/* Badge Icon */}
              <div className={cn(
                "text-2xl mb-2",
                !isUnlocked && "grayscale"
              )}>
                {isUnlocked ? badge.icon : '🔒'}
              </div>

              {/* Badge Info */}
              <h4 className={cn(
                "font-semibold text-sm mb-1",
                isUnlocked ? "text-white" : "text-gray-500"
              )}>
                {badge.name}
              </h4>
              
              <p className={cn(
                "text-xs",
                isUnlocked ? "text-gray-300" : "text-gray-600"
              )}>
                {badge.description}
              </p>

              {/* Rarity Indicator */}
              <div className={cn(
                "absolute top-1 right-1 px-1 py-0.5 rounded text-xs font-bold uppercase",
                isUnlocked ? rarityColor : "bg-gray-700 text-gray-500"
              )}>
                {badge.rarity[0]}
              </div>

              {/* Lock Overlay */}
              {!isUnlocked && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-900/50 rounded-lg">
                  <Lock className="h-6 w-6 text-gray-500" />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Next Badge Hint */}
      {userBadges.length < availableBadges.length && (
        <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-3">
          <div className="flex items-center space-x-2 mb-1">
            <Award className="h-4 w-4 text-blue-400" />
            <span className="text-sm font-semibold text-blue-400">Next Badge</span>
          </div>
          <p className="text-xs text-gray-300">
            Keep participating in sessions to unlock more achievements!
          </p>
        </div>
      )}
    </div>
  );
}
