import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, TrendingUp, Music } from 'lucide-react';
import { cn } from '@/lib/utils';

interface QuickAddProps {
  onAddTrack: (track: any) => void;
  variant: 'mobile' | 'desktop';
}

// Mock popular tracks - in real app this would come from Spotify API
const popularTracks = [
  {
    id: 'popular_1',
    name: 'Blinding Lights',
    artists: [{ name: 'The Weeknd' }],
    album: { name: 'After Hours', images: [{ url: '/placeholder-album.png' }] },
    duration_ms: 200040,
    uri: 'spotify:track:0VjIjW4GlULA4LGoDOLVKN'
  },
  {
    id: 'popular_2',
    name: 'Watermelon Sugar',
    artists: [{ name: '<PERSON>' }],
    album: { name: 'Fine Line', images: [{ url: '/placeholder-album.png' }] },
    duration_ms: 174000,
    uri: 'spotify:track:6UelLqGlWMcVH1E5c4H7lY'
  },
  {
    id: 'popular_3',
    name: 'Levi<PERSON>',
    artists: [{ name: '<PERSON><PERSON>' }],
    album: { name: 'Future Nostalgia', images: [{ url: '/placeholder-album.png' }] },
    duration_ms: 203064,
    uri: 'spotify:track:463CkQjx2Zk1yXoBuierM9'
  },
  {
    id: 'popular_4',
    name: 'Good 4 U',
    artists: [{ name: 'Olivia Rodrigo' }],
    album: { name: 'SOUR', images: [{ url: '/placeholder-album.png' }] },
    duration_ms: 178147,
    uri: 'spotify:track:4ZtFanR9U6ndgddUvNcjcG'
  },
];

export function QuickAdd({ onAddTrack, variant }: QuickAddProps) {
  const isMobile = variant === 'mobile';

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center space-x-2 mb-4">
        <TrendingUp className="h-5 w-5 text-green-400" />
        <span className="text-sm text-gray-400">Popular right now</span>
      </div>

      <div className={cn(
        "grid gap-3",
        isMobile ? "grid-cols-1" : "grid-cols-2"
      )}>
        {popularTracks.map((track) => (
          <div
            key={track.id}
            className="bg-gray-800/50 rounded-lg border border-gray-700/50 p-3 transition-all duration-200 hover:bg-gray-700/50"
          >
            <div className="flex items-center space-x-3">
              {/* Album Art */}
              <div className={cn(
                "flex-shrink-0 rounded overflow-hidden",
                isMobile ? "w-12 h-12" : "w-14 h-14"
              )}>
                <img
                  src={track.album.images[0]?.url || '/placeholder-album.png'}
                  alt={`${track.name} album art`}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                <h4 className={cn(
                  "font-semibold text-white truncate",
                  isMobile ? "text-sm" : "text-base"
                )}>
                  {track.name}
                </h4>
                <p className={cn(
                  "text-gray-400 truncate",
                  isMobile ? "text-xs" : "text-sm"
                )}>
                  {track.artists.map(artist => artist.name).join(', ')}
                </p>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-xs text-gray-500">
                    {track.album.name}
                  </span>
                  <span className="text-xs text-gray-500">•</span>
                  <span className="text-xs text-gray-500">
                    {formatDuration(track.duration_ms)}
                  </span>
                </div>
              </div>

              {/* Add Button */}
              <Button
                onClick={() => onAddTrack(track)}
                size={isMobile ? "sm" : "default"}
                className="bg-green-600 hover:bg-green-700 text-white flex-shrink-0"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* More suggestions button */}
      <div className="text-center pt-2">
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-400 hover:text-white"
          onClick={() => {/* TODO: Load more suggestions */}}
        >
          <Music className="h-4 w-4 mr-1" />
          More suggestions
        </Button>
      </div>
    </div>
  );
}
