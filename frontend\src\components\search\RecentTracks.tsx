import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RecentTracksProps {
  tracks: any[];
  onAddTrack: (track: any) => void;
  variant: 'mobile' | 'desktop';
}

export function RecentTracks({ tracks, onAddTrack, variant }: RecentTracksProps) {
  const isMobile = variant === 'mobile';

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatTimeAgo = (date: string | Date) => {
    const now = new Date();
    const trackDate = new Date(date);
    const diffMs = now.getTime() - trackDate.getTime();
    const diffMins = Math.floor(diffMs / 1000 / 60);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  if (tracks.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-400">No recent tracks</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {tracks.map((track) => (
        <div
          key={track.id}
          className="bg-gray-800/50 rounded-lg border border-gray-700/50 p-3 transition-all duration-200 hover:bg-gray-700/50"
        >
          <div className="flex items-center space-x-3">
            {/* Album Art */}
            <div className={cn(
              "flex-shrink-0 rounded overflow-hidden",
              isMobile ? "w-12 h-12" : "w-14 h-14"
            )}>
              <img
                src={track.imageUrl || '/placeholder-album.png'}
                alt={`${track.title} album art`}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Track Info */}
            <div className="flex-1 min-w-0">
              <h4 className={cn(
                "font-semibold text-white truncate",
                isMobile ? "text-sm" : "text-base"
              )}>
                {track.title}
              </h4>
              <p className={cn(
                "text-gray-400 truncate",
                isMobile ? "text-xs" : "text-sm"
              )}>
                {track.artist}
              </p>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500">
                  Added by {track.submitter?.user?.displayName || track.submitter?.name}
                </span>
                <span className="text-xs text-gray-500">•</span>
                <span className="text-xs text-gray-500">
                  {formatTimeAgo(track.addedAt)}
                </span>
                <span className="text-xs text-gray-500">•</span>
                <span className="text-xs text-gray-500">
                  {formatDuration(track.duration)}
                </span>
              </div>
              
              {/* Score indicator */}
              <div className="flex items-center space-x-2 mt-1">
                <span className={cn(
                  "text-xs font-semibold",
                  track.score > 0 ? "text-green-400" : 
                  track.score < 0 ? "text-red-400" : "text-gray-400"
                )}>
                  {track.score > 0 ? '+' : ''}{track.score} pts
                </span>
              </div>
            </div>

            {/* Add Again Button */}
            <Button
              onClick={() => onAddTrack({
                id: track.spotifyId,
                name: track.title,
                artists: [{ name: track.artist }],
                album: { 
                  name: track.album,
                  images: [{ url: track.imageUrl }]
                },
                duration_ms: track.duration,
                uri: `spotify:track:${track.spotifyId}`
              })}
              size={isMobile ? "sm" : "default"}
              variant="outline"
              className="flex-shrink-0"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Again
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}
