import React, { useState, useEffect } from 'react';
import { UnifiedPlayer } from '@/components/player/UnifiedPlayer';
import { PlaylistQueue } from '@/components/playlist/PlaylistQueue';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { cn } from '@/lib/utils';

interface PlayerTabProps {
  session: any;
  isHost: boolean;
  isGuest: boolean;
  onVote?: (trackId: string, emoji: string, value: number) => void;
  onRemoveTrack?: (trackId: string) => void;
  onPinTrack?: (trackId: string) => void;
  onPlayNext?: () => void;
  onPlayPrevious?: () => void;
}

export function PlayerTab({
  session,
  isHost,
  isGuest,
  onVote,
  onRemoveTrack,
  onPinTrack,
  onPlayNext,
  onPlayPrevious
}: PlayerTabProps) {
  const { isMobile } = useDeviceDetection();
  const [currentTrack, setCurrentTrack] = useState(null);

  // Find the currently playing track
  useEffect(() => {
    const playingTrack = session?.tracks?.find((track: any) => track.isPlaying);
    setCurrentTrack(playingTrack || null);
  }, [session?.tracks]);

  // Separate tracks into current, upcoming, and played
  const tracks = session?.tracks || [];
  const upcomingTracks = tracks
    .filter((track: any) => !track.hasPlayed && !track.isPlaying)
    .sort((a: any, b: any) => b.score - a.score); // Sort by score

  const playedTracks = tracks
    .filter((track: any) => track.hasPlayed)
    .sort((a: any, b: any) => new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime());

  if (isMobile) {
    return (
      <div className="h-full flex flex-col bg-gray-900">
        {/* Current Track Player - Takes priority on mobile */}
        {currentTrack && (
          <div className="flex-shrink-0">
            <UnifiedPlayer
              currentTrack={currentTrack}
              session={session}
              isHost={isHost}
              isGuest={isGuest}
              variant="mobile"
              onPlay={onPlayNext}
              onPause={() => {/* TODO: Implement pause */}}
              onNext={onPlayNext}
              onPrevious={onPlayPrevious}
              onVote={onVote}
            />
          </div>
        )}

        {/* Queue - Scrollable */}
        <div className="flex-1 overflow-hidden">
          <PlaylistQueue
            upcomingTracks={upcomingTracks}
            playedTracks={playedTracks}
            currentTrack={currentTrack}
            session={session}
            isHost={isHost}
            isGuest={isGuest}
            variant="mobile"
            onVote={onVote}
            onPin={onPinTrack}
            onRemove={onRemoveTrack}
          />
        </div>
      </div>
    );
  }

  // Desktop layout - side by side
  return (
    <div className="h-full flex bg-gray-900">
      {/* Player Section */}
      <div className="w-1/2 border-r border-gray-700">
        {currentTrack ? (
          <UnifiedPlayer
            currentTrack={currentTrack}
            session={session}
            isHost={isHost}
            isGuest={isGuest}
            variant="desktop"
            onPlay={onPlayNext}
            onPause={() => {/* TODO: Implement pause */}}
            onNext={onPlayNext}
            onPrevious={onPlayPrevious}
            onVote={onVote}
          />
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="text-6xl mb-4">🎵</div>
              <h3 className="text-xl font-semibold mb-2">No track playing</h3>
              <p>Add some music to get the party started!</p>
            </div>
          </div>
        )}
      </div>

      {/* Queue Section */}
      <div className="w-1/2">
        <PlaylistQueue
          upcomingTracks={upcomingTracks}
          playedTracks={playedTracks}
          currentTrack={currentTrack}
          session={session}
          isHost={isHost}
          isGuest={isGuest}
          variant="desktop"
          onVote={onVote}
          onPin={onPinTrack}
          onRemove={onRemoveTrack}
        />
      </div>
    </div>
  );
}
