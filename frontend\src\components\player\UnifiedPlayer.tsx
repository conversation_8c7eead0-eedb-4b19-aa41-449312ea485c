import React, { useState, useEffect } from 'react';
import { Play, Pause, SkipForward, SkipBack, Volume2, VolumeX, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { VoteButtons } from '@/components/ui/VoteButton';
import { cn } from '@/lib/utils';

interface UnifiedPlayerProps {
  currentTrack: any;
  session: any;
  isHost: boolean;
  isGuest: boolean;
  variant: 'mobile' | 'desktop';
  onPlay?: () => void;
  onPause?: () => void;
  onNext?: () => void;
  onPrevious?: () => void;
  onVote?: (emoji: string, value: number) => void;
}

export function UnifiedPlayer({
  currentTrack,
  session,
  isHost,
  isGuest,
  variant,
  onPlay,
  onPause,
  onNext,
  onPrevious,
  onVote
}: UnifiedPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(50);
  const [isMuted, setIsMuted] = useState(false);
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(0);

  // Mock player state for guests
  useEffect(() => {
    if (currentTrack) {
      setDuration(currentTrack.duration || 0);
      setIsPlaying(currentTrack.isPlaying || false);
    }
  }, [currentTrack]);

  // Simulate progress for guests
  useEffect(() => {
    if (!isHost && isPlaying && duration > 0) {
      const interval = setInterval(() => {
        setPosition(prev => {
          const newPos = prev + 1000;
          return newPos >= duration ? 0 : newPos;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isHost, isPlaying, duration]);

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    if (isHost) {
      if (isPlaying) {
        onPause?.();
      } else {
        onPlay?.();
      }
    }
    setIsPlaying(!isPlaying);
  };

  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setVolume(newVolume);
    if (newVolume === 0) {
      setIsMuted(true);
    } else if (isMuted) {
      setIsMuted(false);
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  // Get user's vote for current track
  const userVote = currentTrack?.votes?.find(
    (vote: any) => vote.participantId === session?.currentParticipant?.id
  );

  if (!currentTrack) {
    return null;
  }

  const isMobile = variant === 'mobile';

  return (
    <div className={cn(
      "bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700",
      isMobile ? "rounded-t-3xl p-6" : "rounded-xl p-8 m-4"
    )}>
      {/* Track Info */}
      <div className={cn(
        "flex items-center space-x-4 mb-6",
        isMobile && "flex-col space-x-0 space-y-4 text-center"
      )}>
        {/* Album Art */}
        <div className={cn(
          "relative flex-shrink-0 rounded-lg overflow-hidden shadow-lg",
          isMobile ? "w-48 h-48" : "w-24 h-24"
        )}>
          <img
            src={currentTrack.imageUrl || '/placeholder-album.png'}
            alt={`${currentTrack.title} album art`}
            className="w-full h-full object-cover"
          />
          {!isHost && (
            <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
              Live
            </div>
          )}
        </div>

        {/* Track Details */}
        <div className={cn("flex-1 min-w-0", isMobile && "text-center")}>
          <h3 className={cn(
            "font-bold text-white truncate",
            isMobile ? "text-xl mb-1" : "text-lg"
          )}>
            {currentTrack.title}
          </h3>
          <p className={cn(
            "text-gray-400 truncate",
            isMobile ? "text-base mb-2" : "text-sm"
          )}>
            {currentTrack.artist}
          </p>
          {currentTrack.album && (
            <p className="text-gray-500 text-sm truncate">
              {currentTrack.album}
            </p>
          )}
          <div className="flex items-center space-x-2 mt-2">
            <span className="text-xs text-gray-500">
              Added by {currentTrack.submitter?.user?.displayName || currentTrack.submitter?.name}
            </span>
            <span className="text-green-400 font-semibold text-sm">
              +{currentTrack.score}
            </span>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center space-x-3 text-sm text-gray-400 mb-2">
          <span>{formatTime(position)}</span>
          <div className="flex-1 bg-gray-700 rounded-full h-2 relative">
            <div
              className="bg-purple-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${duration > 0 ? (position / duration) * 100 : 0}%` }}
            />
          </div>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Controls */}
      <div className={cn(
        "flex items-center justify-between",
        isMobile ? "flex-col space-y-4" : "space-x-4"
      )}>
        {/* Playback Controls */}
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={onPrevious}
            disabled={!isHost}
            className="text-gray-400 hover:text-white disabled:opacity-50"
          >
            <SkipBack className="h-5 w-5" />
          </Button>
          
          <Button
            size="icon"
            onClick={handlePlayPause}
            disabled={!isHost}
            className={cn(
              "bg-purple-600 hover:bg-purple-700 text-white",
              isMobile ? "w-16 h-16" : "w-12 h-12"
            )}
          >
            {isPlaying ? (
              <Pause className={cn("h-6 w-6", isMobile && "h-8 w-8")} />
            ) : (
              <Play className={cn("h-6 w-6", isMobile && "h-8 w-8")} />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={onNext}
            disabled={!isHost}
            className="text-gray-400 hover:text-white disabled:opacity-50"
          >
            <SkipForward className="h-5 w-5" />
          </Button>
        </div>

        {/* Volume Control (Host only) */}
        {isHost && !isMobile && (
          <div className="flex items-center space-x-2 w-32">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMute}
              className="text-gray-400 hover:text-white"
            >
              {isMuted || volume === 0 ? (
                <VolumeX className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" />
              )}
            </Button>
            <Slider
              value={[isMuted ? 0 : volume]}
              onValueChange={handleVolumeChange}
              max={100}
              step={1}
              className="flex-1"
            />
          </div>
        )}

        {/* Host indicator for guests */}
        {!isHost && (
          <div className="text-xs text-gray-500 flex items-center space-x-1">
            <span>Controlled by host</span>
          </div>
        )}
      </div>

      {/* Voting Section */}
      {session?.settings?.votingEnabled && onVote && (
        <div className="mt-6 pt-6 border-t border-gray-700">
          <VoteButtons
            onVote={onVote}
            userVote={userVote}
            variant={isMobile ? 'large' : 'normal'}
          />
        </div>
      )}
    </div>
  );
}
