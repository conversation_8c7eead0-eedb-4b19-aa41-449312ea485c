import React, { useState, useEffect } from 'react';
import { MusicSearch } from '@/components/search/MusicSearch';
import { QuickAdd } from '@/components/search/QuickAdd';
import { RecentTracks } from '@/components/search/RecentTracks';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { Search, Clock, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SearchTabProps {
  session: any;
  isHost: boolean;
  isGuest: boolean;
  onAddTrack?: (track: any) => void;
}

export function SearchTab({ session, isHost, isGuest, onAddTrack }: SearchTabProps) {
  const { isMobile } = useDeviceDetection();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [recentTracks, setRecentTracks] = useState([]);

  // Get recently added tracks for suggestions
  useEffect(() => {
    if (session?.tracks) {
      const recent = session.tracks
        .filter((track: any) => !track.hasPlayed)
        .sort((a: any, b: any) => new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime())
        .slice(0, 5);
      setRecentTracks(recent);
    }
  }, [session?.tracks]);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      // TODO: Implement actual Spotify search
      // const results = await spotifyService.search(query);
      // setSearchResults(results);

      // Mock results for now
      setTimeout(() => {
        setSearchResults([]);
        setIsSearching(false);
      }, 1000);
    } catch (error) {
      console.error('Search error:', error);
      setIsSearching(false);
    }
  };

  const handleAddTrack = async (track: any) => {
    try {
      onAddTrack?.(track);
    } catch (error) {
      console.error('Error adding track:', error);
    }
  };

  return (
    <div className={cn(
      "h-full bg-gray-900 flex flex-col",
      isMobile ? "p-4" : "p-6"
    )}>
      {/* Header */}
      <div className="flex-shrink-0 mb-6">
        <div className="flex items-center space-x-3 mb-4">
          <Search className="h-6 w-6 text-purple-400" />
          <h1 className="text-2xl font-bold text-white">Add Music</h1>
        </div>
        <p className="text-gray-400">
          Search for tracks and add them to the queue
        </p>
      </div>

      {/* Search Section */}
      <div className="flex-shrink-0 mb-6">
        <MusicSearch
          onSearch={handleSearch}
          searchResults={searchResults}
          isSearching={isSearching}
          onAddTrack={handleAddTrack}
          variant={isMobile ? 'mobile' : 'desktop'}
        />
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-y-auto space-y-6">
        {/* Quick Add Suggestions */}
        {!searchQuery && (
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="h-5 w-5 text-green-400" />
              <h3 className="text-lg font-semibold text-white">Quick Add</h3>
            </div>
            <QuickAdd
              onAddTrack={handleAddTrack}
              variant={isMobile ? 'mobile' : 'desktop'}
            />
          </div>
        )}

        {/* Recent Tracks */}
        {!searchQuery && recentTracks.length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Clock className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Recently Added</h3>
            </div>
            <RecentTracks
              tracks={recentTracks}
              onAddTrack={handleAddTrack}
              variant={isMobile ? 'mobile' : 'desktop'}
            />
          </div>
        )}

        {/* Empty State */}
        {!searchQuery && recentTracks.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎵</div>
            <h3 className="text-xl font-semibold text-gray-400 mb-2">
              Start searching for music
            </h3>
            <p className="text-gray-500">
              Use the search bar above to find tracks to add to the queue
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
