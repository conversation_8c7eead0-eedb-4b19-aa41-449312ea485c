import React from 'react';
import { TabType } from '@/components/navigation/BottomNavigation';
import { PlayerTab } from '@/components/tabs/PlayerTab';
import { StatisticsTab } from '@/components/tabs/StatisticsTab';
import { ProfileTab } from '@/components/tabs/ProfileTab';
import { SearchTab } from '@/components/tabs/SearchTab';
import { Music, BarChart3, User, Search, Users, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';

interface DesktopLayoutProps {
  children?: React.ReactNode;
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  session: any;
  isHost: boolean;
  isGuest: boolean;
  onVote?: (trackId: string, emoji: string, value: number) => void;
  onAddTrack?: (track: any) => void;
  onRemoveTrack?: (trackId: string) => void;
  onPinTrack?: (trackId: string) => void;
  onPlayNext?: () => void;
  onPlayPrevious?: () => void;
}

const sidebarItems = [
  { id: 'player' as TabType, icon: Music, label: 'Player & Queue' },
  { id: 'search' as TabType, icon: Search, label: 'Search Music' },
  { id: 'statistics' as TabType, icon: BarChart3, label: 'Statistics' },
  { id: 'profile' as TabType, icon: User, label: 'Profile' },
];

export function DesktopLayout({
  children,
  activeTab,
  onTabChange,
  session,
  isHost,
  isGuest,
  onVote,
  onAddTrack,
  onRemoveTrack,
  onPinTrack,
  onPlayNext,
  onPlayPrevious
}: DesktopLayoutProps) {
  const navigate = useNavigate();

  const renderMainContent = () => {
    switch (activeTab) {
      case 'player':
        return (
          <PlayerTab
            session={session}
            isHost={isHost}
            isGuest={isGuest}
            onVote={onVote}
            onRemoveTrack={onRemoveTrack}
            onPinTrack={onPinTrack}
            onPlayNext={onPlayNext}
            onPlayPrevious={onPlayPrevious}
          />
        );
      case 'statistics':
        return (
          <StatisticsTab
            session={session}
            isHost={isHost}
            isGuest={isGuest}
          />
        );
      case 'profile':
        return (
          <ProfileTab
            session={session}
            isHost={isHost}
            isGuest={isGuest}
          />
        );
      case 'search':
        return (
          <SearchTab
            session={session}
            isHost={isHost}
            isGuest={isGuest}
            onAddTrack={onAddTrack}
          />
        );
      default:
        return children;
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white flex">
      {/* Sidebar Navigation */}
      <aside className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <Music className="h-8 w-8 text-purple-500" />
            <div>
              <h1 className="text-xl font-bold">{session?.name}</h1>
              <p className="text-sm text-gray-400">Code: {session?.code}</p>
            </div>
          </div>
        </div>

        {/* Navigation Items */}
        <nav className="flex-1 p-4 space-y-2">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={cn(
                  "w-full flex items-center space-x-3 px-4 py-3 rounded-lg",
                  "transition-all duration-200",
                  isActive
                    ? "bg-purple-600/20 text-purple-400 border border-purple-600/30"
                    : "text-gray-400 hover:text-gray-300 hover:bg-gray-700/50"
                )}
              >
                <Icon className="h-5 w-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            );
          })}
        </nav>

        {/* Session Info & Actions */}
        <div className="p-4 border-t border-gray-700 space-y-3">
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Users className="h-4 w-4" />
            <span>{session?.participants?.length || 0} participants</span>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/')}
            className="w-full"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Leave Session
          </Button>
        </div>
      </aside>

      {/* Main Content */}
      <main className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto">
          {renderMainContent()}
        </div>
      </main>
    </div>
  );
}
