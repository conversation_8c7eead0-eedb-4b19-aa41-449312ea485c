import React, { useState, useEffect } from 'react';
import { BottomNavigation, TabType } from '@/components/navigation/BottomNavigation';
import { DesktopLayout } from './DesktopLayout';
import { MobileLayout } from './MobileLayout';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  session: any;
  isHost: boolean;
  isGuest: boolean;
  onTabChange?: (tab: TabType) => void;
  defaultTab?: TabType;
  onVote?: (trackId: string, emoji: string, value: number) => void;
  onAddTrack?: (track: any) => void;
  onRemoveTrack?: (trackId: string) => void;
  onPinTrack?: (trackId: string) => void;
  onPlayNext?: () => void;
  onPlayPrevious?: () => void;
}

export function ResponsiveLayout({
  children,
  session,
  isHost,
  isGuest,
  onTabChange,
  defaultTab = 'player',
  onVote,
  onAddTrack,
  onRemoveTrack,
  onPinTrack,
  onPlayNext,
  onPlayPrevious
}: ResponsiveLayoutProps) {
  const [activeTab, setActiveTab] = useState<TabType>(defaultTab);
  const { isMobile, isTablet, isDesktop } = useDeviceDetection();

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    onTabChange?.(tab);
  };

  // Use mobile layout for phones and tablets, desktop layout for larger screens
  const shouldUseMobileLayout = isMobile || isTablet;

  if (shouldUseMobileLayout) {
    return (
      <MobileLayout
        activeTab={activeTab}
        onTabChange={handleTabChange}
        session={session}
        isHost={isHost}
        isGuest={isGuest}
        onVote={onVote}
        onAddTrack={onAddTrack}
        onRemoveTrack={onRemoveTrack}
        onPinTrack={onPinTrack}
        onPlayNext={onPlayNext}
        onPlayPrevious={onPlayPrevious}
      >
        {children}
      </MobileLayout>
    );
  }

  return (
    <DesktopLayout
      activeTab={activeTab}
      onTabChange={handleTabChange}
      session={session}
      isHost={isHost}
      isGuest={isGuest}
      onVote={onVote}
      onAddTrack={onAddTrack}
      onRemoveTrack={onRemoveTrack}
      onPinTrack={onPinTrack}
      onPlayNext={onPlayNext}
      onPlayPrevious={onPlayPrevious}
    >
      {children}
    </DesktopLayout>
  );
}
