import { Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { SpotifyService } from '../services/spotify.service';
import { AuthRequest } from '../middleware/auth.middleware';
// import { redis } from '../config/redis';

const prisma = new PrismaClient();

export class TrackController {
  /**
   * Search tracks on Spotify
   */
  static async searchTracks(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { query, limit = 20, offset = 0 } = req.query;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      if (!query || typeof query !== 'string') {
        res.status(400).json({ error: 'Search query is required' });
        return;
      }

      // Get user's Spotify access token
      const accessToken = await SpotifyService.getCachedAccessToken(userId);
      if (!accessToken) {
        res.status(401).json({ error: 'Spotify authentication required' });
        return;
      }

      // Search tracks on Spotify
      const results = await SpotifyService.searchTracks(
        query,
        accessToken,
        Number(limit),
        Number(offset)
      );

      res.json(results);
    } catch (error) {
      console.error('Error searching tracks:', error);
      res.status(500).json({ error: 'Failed to search tracks' });
    }
  }

  /**
   * Search tracks on Spotify (for guests using host's token)
   */
  static async searchTracksForGuest(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { query, limit = 20, offset = 0 } = req.query;

      if (!query || typeof query !== 'string') {
        res.status(400).json({ error: 'Search query is required' });
        return;
      }

      // For authenticated users, use their own token
      if (req.user?.userId) {
        const accessToken = await SpotifyService.getCachedAccessToken(req.user.userId);
        if (accessToken) {
          const results = await SpotifyService.searchTracks(
            query,
            accessToken,
            Number(limit),
            Number(offset)
          );
          res.json(results);
          return;
        }
      }

      // For guests, use host's token
      if (req.guest?.sessionId) {
        const hostAccessToken = await SpotifyService.getHostAccessToken(req.guest.sessionId);
        if (!hostAccessToken) {
          res.status(401).json({ error: 'Host Spotify authentication required' });
          return;
        }

        const results = await SpotifyService.searchTracks(
          query,
          hostAccessToken,
          Number(limit),
          Number(offset)
        );
        res.json(results);
        return;
      }

      res.status(401).json({ error: 'Authentication required' });
    } catch (error) {
      console.error('Error searching tracks for guest:', error);
      res.status(500).json({ error: 'Failed to search tracks' });
    }
  }

  /**
   * Add track to session (unified for both authenticated users and guests)
   */
  static async addTrack(req: AuthRequest, res: Response): Promise<void> {
    // Delegate to the unified add track function
    return TrackController.addTrackForGuest(req, res);
  }

  /**
   * Vote on a track (unified for both authenticated users and guests)
   */
  static async voteTrack(req: AuthRequest, res: Response): Promise<void> {
    // Delegate to the unified vote function
    return TrackController.voteTrackForGuest(req, res);
  }

  /**
   * Vote on a track (unified for both authenticated users and guests)
   */
  static async voteTrackForGuest(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { sessionCode, trackId } = req.params;
      const { emoji, value } = req.body;

      console.log('voteTrack called:', { sessionCode, trackId, emoji, value, valueType: typeof value, hasUser: !!req.user, hasGuest: !!req.guest });

      if (!emoji || typeof emoji !== 'string') {
        console.log('Invalid emoji:', emoji);
        res.status(400).json({ error: 'Emoji is required' });
        return;
      }

      // Convert value to number if it's a string
      const numericValue = typeof value === 'string' ? parseInt(value, 10) : value;

      if (numericValue === undefined || isNaN(numericValue) || numericValue < -2 || numericValue > 3) {
        console.log('Invalid vote value:', { original: value, converted: numericValue });
        res.status(400).json({ error: 'Invalid vote value. Must be between -2 and 3' });
        return;
      }

      let session, participant;

      // For authenticated users
      if (req.user?.userId) {
        session = await prisma.session.findUnique({
          where: { code: sessionCode.toUpperCase() },
        });

        if (!session) {
          res.status(404).json({ error: 'Session not found' });
          return;
        }

        participant = await prisma.sessionParticipant.findUnique({
          where: {
            sessionId_userId: {
              sessionId: session.id,
              userId: req.user.userId,
            },
          },
        });

        if (!participant) {
          res.status(403).json({ error: 'Not a participant of this session' });
          return;
        }
      }
      // For guests
      else if (req.guest) {
        if (req.guest.sessionCode.toUpperCase() !== sessionCode.toUpperCase()) {
          res.status(403).json({ error: 'Session code mismatch' });
          return;
        }

        session = await prisma.session.findUnique({
          where: { id: req.guest.sessionId },
        });

        if (!session) {
          res.status(404).json({ error: 'Session not found' });
          return;
        }

        participant = await prisma.sessionParticipant.findUnique({
          where: { id: req.guest.participantId },
        });

        if (!participant) {
          res.status(403).json({ error: 'Guest participant not found' });
          return;
        }
      } else {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      // Check session settings
      const settings = session.settings as any;
      if (!settings.votingEnabled) {
        res.status(403).json({ error: 'Voting is disabled' });
        return;
      }

      // Get track
      const track = await prisma.track.findFirst({
        where: {
          id: trackId,
          sessionId: session.id,
        },
      });

      if (!track) {
        res.status(404).json({ error: 'Track not found' });
        return;
      }

      // Check if user already voted on this track (regardless of emoji)
      const existingVote = await prisma.vote.findFirst({
        where: {
          trackId: track.id,
          userId: participant.id,
        },
      });

      let vote;
      let pointsChange = 0;

      if (existingVote) {
        // Si l'utilisateur vote avec le même emoji et la même valeur, on supprime le vote (toggle)
        if (existingVote.emoji === emoji && existingVote.value === numericValue) {
          await prisma.vote.delete({
            where: { id: existingVote.id },
          });

          // Retirer la valeur du score
          await prisma.track.update({
            where: { id: track.id },
            data: {
              score: { increment: -existingVote.value },
            },
          });

          res.json({ vote: null, pointsAwarded: 0, action: 'removed' });
          return;
        } else {
          // Mettre à jour le vote existant avec le nouvel emoji et valeur
          const oldValue = existingVote.value;
          vote = await prisma.vote.update({
            where: { id: existingVote.id },
            data: {
              emoji,
              value: numericValue
            },
          });

          // Calculer le changement de score
          const scoreChange = numericValue - oldValue;
          await prisma.track.update({
            where: { id: track.id },
            data: {
              score: { increment: scoreChange },
            },
          });

          // Attribuer des points pour les votes positifs (seulement pour les utilisateurs authentifiés)
          if (req.user?.userId && numericValue > 0 && oldValue <= 0) {
            pointsChange = Math.max(1, numericValue);
            await prisma.gameEvent.create({
              data: {
                userId: req.user.userId,
                sessionId: session.id,
                type: 'TRACK_UPVOTED',
                points: pointsChange,
                metadata: { trackId: track.id, emoji, value: numericValue },
              },
            });

            await prisma.user.update({
              where: { id: req.user.userId },
              data: {
                points: { increment: pointsChange },
              },
            });
          }
        }
      } else {
        // Créer un nouveau vote
        vote = await prisma.vote.create({
          data: {
            trackId: track.id,
            userId: participant.id,
            emoji,
            value: numericValue,
          },
        });

        // Mettre à jour le score de la piste
        await prisma.track.update({
          where: { id: track.id },
          data: {
            score: { increment: numericValue },
          },
        });

        // Attribuer des points pour les votes positifs (seulement pour les utilisateurs authentifiés)
        if (req.user?.userId && numericValue > 0) {
          pointsChange = Math.max(1, numericValue); // Au moins 1 point pour un vote positif
          await prisma.gameEvent.create({
            data: {
              userId: req.user.userId,
              sessionId: session.id,
              type: 'TRACK_UPVOTED',
              points: pointsChange,
              metadata: { trackId: track.id, emoji, value: numericValue },
            },
          });

          await prisma.user.update({
            where: { id: req.user.userId },
            data: {
              points: { increment: pointsChange },
            },
          });
        }
      }

      res.json({ vote, pointsAwarded: pointsChange });
    } catch (error) {
      console.error('Error voting on track:', error);
      res.status(500).json({ error: 'Failed to vote on track' });
    }
  }

  /**
   * Remove track from session (host or submitter only)
   */
  static async removeTrack(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { sessionCode, trackId } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Get session
      const session = await prisma.session.findUnique({
        where: { code: sessionCode.toUpperCase() },
      });

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      // Get track with submitter info
      const track = await prisma.track.findFirst({
        where: {
          id: trackId,
          sessionId: session.id,
        },
        include: {
          submitter: true,
        },
      });

      if (!track) {
        res.status(404).json({ error: 'Track not found' });
        return;
      }

      // Check permissions
      if (session.hostId !== userId && track.submitter.userId !== userId) {
        res.status(403).json({ error: 'Only the host or track submitter can remove this track' });
        return;
      }

      // Delete track (votes will be cascade deleted)
      await prisma.track.delete({
        where: { id: track.id },
      });

      res.json({ success: true });
    } catch (error) {
      console.error('Error removing track:', error);
      res.status(500).json({ error: 'Failed to remove track' });
    }
  }

  /**
   * Update currently playing track (host only)
   */
  static async updatePlayingTrack(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { sessionCode, trackId } = req.params;
      const userId = req.user?.userId;

      if (!userId) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      // Get session
      const session = await prisma.session.findUnique({
        where: { code: sessionCode.toUpperCase() },
      });

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      // Check if user is host
      if (session.hostId !== userId) {
        res.status(403).json({ error: 'Only the host can update playing track' });
        return;
      }

      // Reset all tracks to not playing
      await prisma.track.updateMany({
        where: {
          sessionId: session.id,
          isPlaying: true,
        },
        data: {
          isPlaying: false,
        },
      });

      // Set new playing track
      const track = await prisma.track.update({
        where: { id: trackId },
        data: {
          isPlaying: true,
          hasPlayed: true,
        },
      });

      // Award points to submitter
      const submitter = await prisma.sessionParticipant.findUnique({
        where: { id: track.submittedBy },
      });

      if (submitter && submitter.userId) {
        await prisma.gameEvent.create({
          data: {
            userId: submitter.userId,
            sessionId: session.id,
            type: 'TRACK_PLAYED',
            points: 10,
            metadata: { trackId: track.id },
          },
        });

        await prisma.user.update({
          where: { id: submitter.userId },
          data: {
            points: { increment: 10 },
          },
        });
      }

      res.json(track);
    } catch (error) {
      console.error('Error updating playing track:', error);
      res.status(500).json({ error: 'Failed to update playing track' });
    }
  }

  /**
   * Add track to session (for guests using host's token)
   */
  static async addTrackForGuest(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { sessionCode } = req.params;
      const { spotifyId } = req.body;

      console.log('addTrackForGuest called:', { sessionCode, spotifyId, hasUser: !!req.user, hasGuest: !!req.guest });

      if (!spotifyId || typeof spotifyId !== 'string') {
        console.log('Invalid spotifyId:', spotifyId);
        res.status(400).json({ error: 'Spotify track ID is required' });
        return;
      }

      let session, participant, accessToken;

      // For authenticated users
      if (req.user?.userId) {
        session = await prisma.session.findUnique({
          where: { code: sessionCode.toUpperCase() },
          include: {
            tracks: {
              where: { spotifyId },
            },
          },
        });

        if (!session) {
          res.status(404).json({ error: 'Session not found' });
          return;
        }

        if (!session.isActive) {
          res.status(400).json({ error: 'Session is no longer active' });
          return;
        }

        participant = await prisma.sessionParticipant.findUnique({
          where: {
            sessionId_userId: {
              sessionId: session.id,
              userId: req.user.userId,
            },
          },
        });

        if (!participant) {
          res.status(403).json({ error: 'Not a participant of this session' });
          return;
        }

        accessToken = await SpotifyService.getCachedAccessToken(req.user.userId);
        if (!accessToken) {
          // Fallback to host's token
          accessToken = await SpotifyService.getHostAccessToken(session.id);
        }
      }
      // For guests
      else if (req.guest) {
        if (req.guest.sessionCode.toUpperCase() !== sessionCode.toUpperCase()) {
          res.status(403).json({ error: 'Session code mismatch' });
          return;
        }

        session = await prisma.session.findUnique({
          where: { id: req.guest.sessionId },
          include: {
            tracks: {
              where: { spotifyId },
            },
          },
        });

        if (!session) {
          res.status(404).json({ error: 'Session not found' });
          return;
        }

        if (!session.isActive) {
          res.status(400).json({ error: 'Session is no longer active' });
          return;
        }

        participant = await prisma.sessionParticipant.findUnique({
          where: { id: req.guest.participantId },
        });

        if (!participant) {
          res.status(403).json({ error: 'Guest participant not found' });
          return;
        }

        // Use host's token for guests
        accessToken = await SpotifyService.getHostAccessToken(session.id);
      } else {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      if (!accessToken) {
        res.status(401).json({ error: 'Spotify authentication required' });
        return;
      }

      // Check if track already exists in session
      const existingTrack = await prisma.track.findFirst({
        where: {
          sessionId: session.id,
          spotifyId: spotifyId,
        },
        include: {
          votes: true,
        },
      });

      if (existingTrack) {
        // Si la track existe déjà, donner un bonus de score pour la popularité
        // et retourner la track existante avec un message
        const bonusScore = 2; // Bonus pour track populaire
        await prisma.track.update({
          where: { id: existingTrack.id },
          data: {
            score: { increment: bonusScore },
          },
        });

        res.status(200).json({
          ...existingTrack,
          message: 'Track déjà ajoutée, bonus de popularité appliqué!',
          bonusApplied: true,
          bonusScore: bonusScore,
        });
        return;
      }

      // Check session settings
      const settings = session.settings as any;
      if (!settings.allowSubmissions) {
        res.status(403).json({ error: 'Track submissions are disabled' });
        return;
      }

      // Count user's tracks
      const userTrackCount = await prisma.track.count({
        where: {
          sessionId: session.id,
          submittedBy: participant.id,
        },
      });

      if (userTrackCount >= (settings.maxTracksPerUser || 10)) {
        res.status(403).json({ error: 'Maximum track limit reached' });
        return;
      }

      // Check if this is the first track in the session
      const existingTracks = await prisma.track.count({
        where: { sessionId: session.id },
      });
      const isFirstTrack = existingTracks === 0;

      const trackDetails = await SpotifyService.getTrack(spotifyId, accessToken);

      // Add track to session
      const track = await prisma.track.create({
        data: {
          sessionId: session.id,
          spotifyId: trackDetails.id,
          submittedBy: participant.id,
          title: trackDetails.name,
          artist: trackDetails.artists.map(a => a.name).join(', '),
          album: trackDetails.album.name,
          duration: trackDetails.duration_ms,
          imageUrl: trackDetails.album.images[0]?.url || null,
          // If it's the first track, set it as playing automatically
          isPlaying: isFirstTrack,
        },
        include: {
          submitter: {
            include: {
              user: {
                select: {
                  displayName: true,
                  avatarUrl: true,
                },
              },
            },
          },
        },
      });

      // Track game event (only for authenticated users)
      if (req.user?.userId) {
        await prisma.gameEvent.create({
          data: {
            userId: req.user.userId,
            sessionId: session.id,
            type: 'TRACK_ADDED',
            points: 5,
            metadata: { trackId: track.id },
          },
        });

        // Update user points
        await prisma.user.update({
          where: { id: req.user.userId },
          data: {
            points: { increment: 5 },
          },
        });
      }

      // Return track with additional info for auto-play
      res.status(201).json({
        ...track,
        isFirstTrack,
        shouldAutoPlay: isFirstTrack,
      });
    } catch (error) {
      console.error('Error adding track for guest:', error);
      res.status(500).json({ error: 'Failed to add track' });
    }
  }


}
