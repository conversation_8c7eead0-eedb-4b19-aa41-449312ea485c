import { AuthService } from './auth.service';

declare global {
  interface Window {
    onSpotifyWebPlaybackSDKReady: () => void;
    Spotify: any;
  }
}

export interface SpotifyPlayerState {
  isPlaying: boolean;
  position: number;
  duration: number;
  volume: number;
  currentTrack: any;
  deviceId: string;
  isReady: boolean;
}

export interface SpotifyPlayerCallbacks {
  onReady?: (deviceId: string) => void;
  onStateChange?: (state: any) => void;
  onTokenRefresh?: (token: string) => void;
}

export class SpotifyPlayerService {
  private static instance: SpotifyPlayerService;
  private player: any = null;
  private state: SpotifyPlayerState = {
    isPlaying: false,
    position: 0,
    duration: 0,
    volume: 50,
    currentTrack: null,
    deviceId: '',
    isReady: false,
  };
  private callbacks: SpotifyPlayerCallbacks = {};
  private positionInterval: NodeJS.Timeout | null = null;

  static getInstance(): SpotifyPlayerService {
    if (!SpotifyPlayerService.instance) {
      SpotifyPlayerService.instance = new SpotifyPlayerService();
    }
    return SpotifyPlayerService.instance;
  }

  async initialize(token: string, callbacks: SpotifyPlayerCallbacks = {}): Promise<void> {
    this.callbacks = callbacks;

    return new Promise((resolve, reject) => {
      // Load Spotify SDK if not already loaded
      if (!window.Spotify) {
        const script = document.createElement('script');
        script.src = 'https://sdk.scdn.co/spotify-player.js';
        script.async = true;
        document.body.appendChild(script);
      }

      window.onSpotifyWebPlaybackSDKReady = () => {
        this.createPlayer(token);
        resolve();
      };

      // Timeout after 10 seconds
      setTimeout(() => {
        if (!this.state.isReady) {
          reject(new Error('Spotify SDK failed to load'));
        }
      }, 10000);
    });
  }

  private createPlayer(token: string): void {
    this.player = new window.Spotify.Player({
      name: 'Playhifyy Web Player',
      getOAuthToken: async (cb: (token: string) => void) => {
        try {
          const freshToken = await AuthService.getSpotifyAccessToken();
          cb(freshToken);
          if (this.callbacks.onTokenRefresh && freshToken !== token) {
            this.callbacks.onTokenRefresh(freshToken);
          }
        } catch (error) {
          console.error('Failed to refresh Spotify token:', error);
          cb(token);
        }
      },
      volume: this.state.volume / 100,
    });

    // Error handling
    this.player.addListener('initialization_error', ({ message }: any) => {
      console.error('Failed to initialize', message);
    });

    this.player.addListener('authentication_error', ({ message }: any) => {
      console.error('Failed to authenticate', message);
    });

    this.player.addListener('account_error', ({ message }: any) => {
      console.error('Failed to validate Spotify account', message);
    });

    this.player.addListener('playback_error', ({ message }: any) => {
      console.error('Failed to perform playback', message);
    });

    // Player state changes
    this.player.addListener('player_state_changed', (state: any) => {
      if (!state) return;

      this.state.isPlaying = !state.paused;
      this.state.position = state.position;
      this.state.duration = state.duration;
      this.state.currentTrack = state.track_window.current_track;

      if (this.callbacks.onStateChange) {
        this.callbacks.onStateChange(state);
      }

      // Start/stop position tracking
      if (this.state.isPlaying) {
        this.startPositionTracking();
      } else {
        this.stopPositionTracking();
      }
    });

    // Ready
    this.player.addListener('ready', ({ device_id }: any) => {
      console.log('Spotify Player Ready with Device ID', device_id);
      this.state.deviceId = device_id;
      this.state.isReady = true;
      
      if (this.callbacks.onReady) {
        this.callbacks.onReady(device_id);
      }
    });

    // Not Ready
    this.player.addListener('not_ready', ({ device_id }: any) => {
      console.log('Device ID has gone offline', device_id);
      this.state.isReady = false;
    });

    // Connect to the player
    this.player.connect().then((success: boolean) => {
      if (success) {
        console.log('Successfully connected to Spotify!');
      } else {
        console.error('Failed to connect to Spotify');
      }
    });
  }

  private startPositionTracking(): void {
    if (this.positionInterval) return;
    
    this.positionInterval = setInterval(() => {
      if (this.state.isPlaying && this.state.duration > 0) {
        this.state.position = Math.min(this.state.position + 1000, this.state.duration);
      }
    }, 1000);
  }

  private stopPositionTracking(): void {
    if (this.positionInterval) {
      clearInterval(this.positionInterval);
      this.positionInterval = null;
    }
  }

  // Public methods for player control
  async play(trackUri?: string): Promise<void> {
    if (!this.player || !this.state.isReady) {
      throw new Error('Player not ready');
    }

    if (trackUri) {
      // Play specific track via Spotify API
      const token = await AuthService.getSpotifyAccessToken();
      await fetch(`https://api.spotify.com/v1/me/player/play?device_id=${this.state.deviceId}`, {
        method: 'PUT',
        body: JSON.stringify({ uris: [trackUri] }),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });
    } else {
      // Resume current track
      await this.player.resume();
    }
  }

  async pause(): Promise<void> {
    if (!this.player) throw new Error('Player not ready');
    await this.player.pause();
  }

  async togglePlay(): Promise<void> {
    if (!this.player) throw new Error('Player not ready');
    await this.player.togglePlay();
  }

  async nextTrack(): Promise<void> {
    if (!this.player) throw new Error('Player not ready');
    await this.player.nextTrack();
  }

  async previousTrack(): Promise<void> {
    if (!this.player) throw new Error('Player not ready');
    await this.player.previousTrack();
  }

  async seek(position: number): Promise<void> {
    if (!this.player) throw new Error('Player not ready');
    await this.player.seek(position);
    this.state.position = position;
  }

  async setVolume(volume: number): Promise<void> {
    if (!this.player) throw new Error('Player not ready');
    await this.player.setVolume(volume / 100);
    this.state.volume = volume;
  }

  getState(): SpotifyPlayerState {
    return { ...this.state };
  }

  isReady(): boolean {
    return this.state.isReady;
  }

  getDeviceId(): string {
    return this.state.deviceId;
  }

  disconnect(): void {
    this.stopPositionTracking();
    if (this.player) {
      this.player.disconnect();
    }
  }
}
