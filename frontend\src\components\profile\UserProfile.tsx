import React from 'react';
import { Button } from '@/components/ui/button';
import { User, Crown, LogOut, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';

interface UserProfileProps {
  user: any;
  session: any;
  isHost: boolean;
  isGuest: boolean;
  variant: 'mobile' | 'desktop';
}

export function UserProfile({ user, session, isHost, isGuest, variant }: UserProfileProps) {
  const navigate = useNavigate();
  const isMobile = variant === 'mobile';

  const handleLeaveSession = () => {
    navigate('/');
  };

  if (isGuest) {
    return (
      <div className="space-y-4">
        <div className="text-center">
          <div className="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="h-10 w-10 text-white" />
          </div>
          <h3 className="text-xl font-bold text-white">Guest User</h3>
          <p className="text-gray-400">Temporary session access</p>
        </div>

        <div className="bg-gray-700/30 rounded-lg p-4">
          <h4 className="font-semibold text-white mb-2">Session Role</h4>
          <p className="text-gray-400 text-sm">
            You're participating as a guest. Create an account to save your progress and unlock achievements.
          </p>
        </div>

        <Button
          onClick={handleLeaveSession}
          variant="outline"
          className="w-full"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Leave Session
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* User Info */}
      <div className="text-center">
        <div className="relative mx-auto mb-4">
          {user?.avatarUrl ? (
            <img
              src={user.avatarUrl}
              alt={user.displayName}
              className="w-20 h-20 rounded-full object-cover mx-auto"
            />
          ) : (
            <div className="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto">
              <User className="h-10 w-10 text-white" />
            </div>
          )}
          {isHost && (
            <div className="absolute -top-1 -right-1 bg-yellow-400 rounded-full p-1">
              <Crown className="h-4 w-4 text-gray-900" />
            </div>
          )}
        </div>
        
        <h3 className="text-xl font-bold text-white">
          {user?.displayName || 'User'}
        </h3>
        <p className="text-gray-400">
          {isHost ? 'Session Host' : 'Participant'}
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-700/30 rounded-lg p-3 text-center">
          <p className="text-2xl font-bold text-purple-400">
            {user?.level || 1}
          </p>
          <p className="text-xs text-gray-400">Level</p>
        </div>
        <div className="bg-gray-700/30 rounded-lg p-3 text-center">
          <p className="text-2xl font-bold text-green-400">
            {user?.points || 0}
          </p>
          <p className="text-xs text-gray-400">Points</p>
        </div>
      </div>

      {/* Current Session Points */}
      {session?.currentParticipant && (
        <div className="bg-purple-900/20 border border-purple-700/30 rounded-lg p-4">
          <h4 className="font-semibold text-white mb-2">Session Progress</h4>
          <div className="flex items-center justify-between">
            <span className="text-gray-400">Points earned</span>
            <span className="font-bold text-purple-400">
              +{session.participants?.find((p: any) => p.id === session.currentParticipant.id)?.points || 0}
            </span>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="space-y-2">
        <Button
          variant="outline"
          className="w-full"
          onClick={() => {/* TODO: Open settings */}}
        >
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
        
        <Button
          onClick={handleLeaveSession}
          variant="outline"
          className="w-full"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Leave Session
        </Button>
      </div>
    </div>
  );
}
