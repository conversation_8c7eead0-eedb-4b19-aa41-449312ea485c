import React from 'react';
import { Star, TrendingUp, Music } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TopTracksProps {
  session: any;
  variant: 'mobile' | 'desktop';
}

export function TopTracks({ session, variant }: TopTracksProps) {
  const isMobile = variant === 'mobile';

  // Get top tracks by score (excluding played tracks)
  const topTracks = (session?.tracks || [])
    .filter((track: any) => !track.hasPlayed)
    .sort((a: any, b: any) => b.score - a.score)
    .slice(0, 5);

  if (topTracks.length === 0) {
    return (
      <div className="text-center py-8">
        <Music className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-400">No tracks in queue</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {topTracks.map((track: any, index: number) => (
        <div
          key={track.id}
          className={cn(
            "flex items-center space-x-3 p-3 rounded-lg",
            "bg-gray-700/30 border border-gray-600/30",
            "transition-all duration-200 hover:bg-gray-600/30"
          )}
        >
          {/* Rank */}
          <div className={cn(
            "flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center",
            index === 0 ? "bg-yellow-400/20 text-yellow-400" :
            index === 1 ? "bg-gray-400/20 text-gray-300" :
            index === 2 ? "bg-orange-400/20 text-orange-400" :
            "bg-gray-600/20 text-gray-400"
          )}>
            <span className="text-xs font-bold">{index + 1}</span>
          </div>

          {/* Album Art */}
          <div className={cn(
            "flex-shrink-0 rounded overflow-hidden",
            isMobile ? "w-8 h-8" : "w-10 h-10"
          )}>
            <img
              src={track.imageUrl || '/placeholder-album.png'}
              alt={`${track.title} album art`}
              className="w-full h-full object-cover"
            />
          </div>

          {/* Track Info */}
          <div className="flex-1 min-w-0">
            <p className={cn(
              "font-semibold text-white truncate",
              isMobile ? "text-sm" : "text-base"
            )}>
              {track.title}
            </p>
            <p className={cn(
              "text-gray-400 truncate",
              isMobile ? "text-xs" : "text-sm"
            )}>
              {track.artist}
            </p>
          </div>

          {/* Score */}
          <div className="flex-shrink-0 text-right">
            <div className="flex items-center space-x-1">
              <TrendingUp className="h-3 w-3 text-green-400" />
              <span className={cn(
                "font-bold text-green-400",
                isMobile ? "text-sm" : "text-base"
              )}>
                +{track.score}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
