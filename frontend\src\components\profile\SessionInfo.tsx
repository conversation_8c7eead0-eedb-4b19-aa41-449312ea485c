import React from 'react';
import { Button } from '@/components/ui/button';
import { Copy, QrCode, Users, Clock, Music, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';

interface SessionInfoProps {
  session: any;
  variant: 'mobile' | 'desktop';
}

export function SessionInfo({ session, variant }: SessionInfoProps) {
  const { toast } = useToast();
  const isMobile = variant === 'mobile';

  const copySessionCode = () => {
    navigator.clipboard.writeText(session.code);
    toast({
      title: "Copied!",
      description: "Session code copied to clipboard",
    });
  };

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleString();
  };

  const getSessionDuration = () => {
    const start = new Date(session.createdAt);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / 1000 / 60);
    
    if (diffMins < 60) {
      return `${diffMins} minutes`;
    } else {
      const hours = Math.floor(diffMins / 60);
      const mins = diffMins % 60;
      return `${hours}h ${mins}m`;
    }
  };

  return (
    <div className="space-y-4">
      {/* Session Code */}
      <div className="bg-purple-900/20 border border-purple-700/30 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-semibold text-white">Session Code</h4>
          <Button
            variant="ghost"
            size="sm"
            onClick={copySessionCode}
            className="text-purple-400 hover:text-purple-300"
          >
            <Copy className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <code className="text-2xl font-bold text-purple-400 tracking-wider">
            {session.code}
          </code>
        </div>
        <p className="text-xs text-gray-400 mt-1">
          Share this code for others to join
        </p>
      </div>

      {/* Session Stats */}
      <div className="grid grid-cols-2 gap-3">
        <div className="bg-gray-700/30 rounded-lg p-3">
          <div className="flex items-center space-x-2 mb-1">
            <Users className="h-4 w-4 text-green-400" />
            <span className="text-sm text-gray-400">Participants</span>
          </div>
          <p className="text-lg font-bold text-white">
            {session.participants?.length || 0}
          </p>
        </div>

        <div className="bg-gray-700/30 rounded-lg p-3">
          <div className="flex items-center space-x-2 mb-1">
            <Music className="h-4 w-4 text-blue-400" />
            <span className="text-sm text-gray-400">Tracks</span>
          </div>
          <p className="text-lg font-bold text-white">
            {session.tracks?.length || 0}
          </p>
        </div>

        <div className="bg-gray-700/30 rounded-lg p-3 col-span-2">
          <div className="flex items-center space-x-2 mb-1">
            <Clock className="h-4 w-4 text-yellow-400" />
            <span className="text-sm text-gray-400">Duration</span>
          </div>
          <p className="text-lg font-bold text-white">
            {getSessionDuration()}
          </p>
        </div>
      </div>

      {/* Session Details */}
      <div className="space-y-3">
        <div className="bg-gray-700/30 rounded-lg p-3">
          <h5 className="font-semibold text-white mb-2">Session Details</h5>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">Name:</span>
              <span className="text-white">{session.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Host:</span>
              <span className="text-white">{session.host?.displayName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Created:</span>
              <span className="text-white">{formatDate(session.createdAt)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Status:</span>
              <span className={cn(
                "font-semibold",
                session.isActive ? "text-green-400" : "text-red-400"
              )}>
                {session.isActive ? "Active" : "Inactive"}
              </span>
            </div>
          </div>
        </div>

        {/* Session Settings */}
        <div className="bg-gray-700/30 rounded-lg p-3">
          <h5 className="font-semibold text-white mb-2">Settings</h5>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">Voting:</span>
              <span className={cn(
                "font-semibold",
                session.settings?.votingEnabled ? "text-green-400" : "text-red-400"
              )}>
                {session.settings?.votingEnabled ? "Enabled" : "Disabled"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Submissions:</span>
              <span className={cn(
                "font-semibold",
                session.settings?.allowSubmissions ? "text-green-400" : "text-red-400"
              )}>
                {session.settings?.allowSubmissions ? "Allowed" : "Disabled"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Max tracks per user:</span>
              <span className="text-white">
                {session.settings?.maxTracksPerUser || "Unlimited"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="space-y-2">
        <Button
          variant="outline"
          className="w-full"
          onClick={() => {/* TODO: Show QR code */}}
        >
          <QrCode className="h-4 w-4 mr-2" />
          Show QR Code
        </Button>
      </div>
    </div>
  );
}
