import React from 'react';
import { UserProfile } from '@/components/profile/UserProfile';
import { BadgeCollection } from '@/components/profile/BadgeCollection';
import { SessionInfo } from '@/components/profile/SessionInfo';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { useAuth } from '@/contexts/AuthContext';
import { User, Award, Info } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProfileTabProps {
  session: any;
  isHost: boolean;
  isGuest: boolean;
}

export function ProfileTab({ session, isHost, isGuest }: ProfileTabProps) {
  const { isMobile } = useDeviceDetection();
  const { user } = useAuth();

  if (isMobile) {
    return (
      <div className="h-full bg-gray-900 overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-gray-900/95 backdrop-blur-lg border-b border-gray-700 p-4 z-10">
          <div className="flex items-center space-x-3">
            <User className="h-6 w-6 text-purple-400" />
            <h1 className="text-2xl font-bold text-white">Profile</h1>
          </div>
        </div>

        {/* Mobile Layout - Stacked */}
        <div className="p-4 space-y-6">
          {/* User Profile */}
          <div>
            <UserProfile 
              user={user} 
              session={session} 
              isHost={isHost} 
              isGuest={isGuest}
              variant="mobile" 
            />
          </div>

          {/* Session Info */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Info className="h-5 w-5 text-blue-400" />
              <h2 className="text-lg font-semibold text-white">Session Info</h2>
            </div>
            <SessionInfo session={session} variant="mobile" />
          </div>

          {/* Badge Collection */}
          {!isGuest && (
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Award className="h-5 w-5 text-yellow-400" />
                <h2 className="text-lg font-semibold text-white">Achievements</h2>
              </div>
              <BadgeCollection user={user} variant="mobile" />
            </div>
          )}
        </div>
      </div>
    );
  }

  // Desktop Layout
  return (
    <div className="h-full bg-gray-900 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-3 mb-2">
          <User className="h-8 w-8 text-purple-400" />
          <h1 className="text-3xl font-bold text-white">Profile & Settings</h1>
        </div>
        <p className="text-gray-400">
          Manage your account and view your achievements
        </p>
      </div>

      {/* Desktop Grid Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
        {/* Left Column */}
        <div className="space-y-6">
          {/* User Profile */}
          <div className="bg-gray-800 rounded-xl p-6">
            <UserProfile 
              user={user} 
              session={session} 
              isHost={isHost} 
              isGuest={isGuest}
              variant="desktop" 
            />
          </div>

          {/* Session Info */}
          <div className="bg-gray-800 rounded-xl p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Info className="h-6 w-6 text-blue-400" />
              <h2 className="text-xl font-semibold text-white">Session Info</h2>
            </div>
            <SessionInfo session={session} variant="desktop" />
          </div>
        </div>

        {/* Right Column - Badge Collection */}
        {!isGuest && (
          <div className="bg-gray-800 rounded-xl p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Award className="h-6 w-6 text-yellow-400" />
              <h2 className="text-xl font-semibold text-white">Achievements</h2>
            </div>
            <BadgeCollection user={user} variant="desktop" />
          </div>
        )}
      </div>
    </div>
  );
}
