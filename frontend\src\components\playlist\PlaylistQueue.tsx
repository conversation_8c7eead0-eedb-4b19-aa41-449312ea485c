import React, { useState } from 'react';
import { TrackCard } from './TrackCard';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, Music, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PlaylistQueueProps {
  upcomingTracks: any[];
  playedTracks: any[];
  currentTrack: any;
  session: any;
  isHost: boolean;
  isGuest: boolean;
  variant: 'mobile' | 'desktop';
  onVote?: (trackId: string, emoji: string, value: number) => void;
  onPin?: (trackId: string) => void;
  onRemove?: (trackId: string) => void;
}

export function PlaylistQueue({
  upcomingTracks,
  playedTracks,
  currentTrack,
  session,
  isHost,
  isGuest,
  variant,
  onVote,
  onPin,
  onRemove
}: PlaylistQueueProps) {
  const [showPlayed, setShowPlayed] = useState(false);
  const isMobile = variant === 'mobile';

  return (
    <div className={cn(
      "h-full flex flex-col",
      isMobile ? "bg-gray-900" : "bg-gray-800"
    )}>
      {/* Header */}
      <div className={cn(
        "flex-shrink-0 border-b border-gray-700",
        isMobile ? "p-4" : "p-6"
      )}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Music className="h-5 w-5 text-purple-400" />
            <h2 className="text-lg font-bold text-white">Queue</h2>
            <span className="bg-purple-600/20 text-purple-400 px-2 py-1 rounded-full text-xs">
              {upcomingTracks.length}
            </span>
          </div>
          
          {playedTracks.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPlayed(!showPlayed)}
              className="text-gray-400 hover:text-white"
            >
              <Clock className="h-4 w-4 mr-1" />
              History ({playedTracks.length})
              {showPlayed ? (
                <ChevronUp className="h-4 w-4 ml-1" />
              ) : (
                <ChevronDown className="h-4 w-4 ml-1" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Queue Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Upcoming Tracks */}
        <div className={cn("space-y-2", isMobile ? "p-4" : "p-6")}>
          {upcomingTracks.length > 0 ? (
            <>
              <h3 className="text-sm font-semibold text-gray-400 mb-3 uppercase tracking-wide">
                Up Next
              </h3>
              {upcomingTracks.map((track, index) => (
                <TrackCard
                  key={track.id}
                  track={track}
                  session={session}
                  isHost={isHost}
                  isGuest={isGuest}
                  variant={variant}
                  position={index + 1}
                  onVote={onVote}
                  onPin={onPin}
                  onRemove={onRemove}
                  showPosition={true}
                />
              ))}
            </>
          ) : (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">🎵</div>
              <h3 className="text-lg font-semibold text-gray-400 mb-2">
                Queue is empty
              </h3>
              <p className="text-gray-500 text-sm">
                {isHost ? "Add some tracks to get started!" : "Ask others to add music!"}
              </p>
            </div>
          )}
        </div>

        {/* Played Tracks (Collapsible) */}
        {showPlayed && playedTracks.length > 0 && (
          <div className={cn(
            "border-t border-gray-700 space-y-2",
            isMobile ? "p-4" : "p-6"
          )}>
            <h3 className="text-sm font-semibold text-gray-400 mb-3 uppercase tracking-wide">
              Recently Played
            </h3>
            {playedTracks.slice(0, 10).map((track) => (
              <TrackCard
                key={track.id}
                track={track}
                session={session}
                isHost={isHost}
                isGuest={isGuest}
                variant={variant}
                onVote={onVote}
                showPosition={false}
                isPlayed={true}
              />
            ))}
            {playedTracks.length > 10 && (
              <div className="text-center py-2">
                <span className="text-xs text-gray-500">
                  And {playedTracks.length - 10} more...
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
