import { useState, useEffect } from 'react';
import { Play, Pause, SkipForward, SkipBack, Volume2, VolumeX, Music } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { SpotifyPlayerService, SpotifyPlayerState } from '@/services/spotify-player.service';
import { VOTE_EMOJIS } from '@/constants/vote-emojis';

interface SpotifyPlayerProps {
  token: string;
  trackUri?: string;
  onPlayerReady?: (deviceId: string) => void;
  onStateChange?: (state: any) => void;
  onTokenRefresh?: (newToken: string) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  currentTrackData?: any;
  onVote?: (emoji: string, value: number) => void;
  userVote?: { emoji: string; value: number } | null;
  votingEnabled?: boolean;
  isHost?: boolean;
  showControls?: boolean;
}

export function SpotifyPlayer({
  token,
  trackUri,
  onPlayerReady,
  onStateChange,
  onTokenRefresh,
  onNext,
  onPrevious,
  currentTrackData,
  onVote,
  userVote,
  votingEnabled = false,
  isHost = false,
  showControls = true
}: SpotifyPlayerProps) {
  const [playerState, setPlayerState] = useState<SpotifyPlayerState>({
    isPlaying: false,
    position: 0,
    duration: 0,
    volume: 50,
    currentTrack: null,
    deviceId: '',
    isReady: false,
  });
  const [isMuted, setIsMuted] = useState(false);
  const [previousVolume, setPreviousVolume] = useState(50);
  const { toast } = useToast();
  const playerService = SpotifyPlayerService.getInstance();

  // Initialiser le player
  useEffect(() => {
    const initPlayer = async () => {
      try {
        await playerService.initialize(token, {
          onReady: (deviceId) => {
            console.log('Player ready with device ID:', deviceId);
            setPlayerState(prev => ({ ...prev, deviceId, isReady: true }));
            if (onPlayerReady) onPlayerReady(deviceId);
            
            toast({
              title: "Lecteur Spotify prêt",
              description: "Vous pouvez maintenant contrôler la lecture",
            });
          },
          onStateChange: (state) => {
            if (state) {
              setPlayerState(prev => ({
                ...prev,
                isPlaying: !state.paused,
                position: state.position,
                duration: state.duration,
                currentTrack: state.track_window.current_track,
              }));
              if (onStateChange) onStateChange(state);
            }
          },
          onTokenRefresh: (newToken) => {
            if (onTokenRefresh) onTokenRefresh(newToken);
          }
        });
      } catch (error) {
        console.error('Failed to initialize Spotify player:', error);
        toast({
          title: "Erreur Spotify",
          description: "Impossible d'initialiser le lecteur",
          variant: "destructive",
        });
      }
    };

    initPlayer();

    return () => {
      playerService.disconnect();
    };
  }, [token]);

  // Jouer une track spécifique (seulement pour l'hôte)
  useEffect(() => {
    if (trackUri && playerState.isReady && isHost) {
      const playTrack = async () => {
        try {
          await playerService.play(trackUri);
          console.log('Track started:', trackUri);
        } catch (error) {
          console.error('Error playing track:', error);
          // Retry après 2 secondes
          setTimeout(() => playTrack(), 2000);
        }
      };

      // Délai pour s'assurer que le player est prêt
      setTimeout(() => playTrack(), 500);
    }
  }, [trackUri, playerState.isReady, isHost]);

  // Mettre à jour l'état du player
  useEffect(() => {
    const interval = setInterval(() => {
      const currentState = playerService.getState();
      setPlayerState(currentState);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Fonctions de contrôle (seulement pour l'hôte)
  const togglePlay = async () => {
    if (!isHost) return;
    try {
      await playerService.togglePlay();
    } catch (error) {
      console.warn('Error toggling play:', error);
    }
  };

  const nextTrack = () => {
    if (!isHost) return;
    if (onNext) {
      onNext();
    } else {
      playerService.nextTrack().catch(console.warn);
    }
  };

  const previousTrack = () => {
    if (!isHost) return;
    if (onPrevious) {
      onPrevious();
    } else {
      playerService.previousTrack().catch(console.warn);
    }
  };

  const handleVolumeChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!isHost) return;
    const newVolume = parseInt(e.target.value);
    setPlayerState(prev => ({ ...prev, volume: newVolume }));
    if (newVolume > 0) {
      setIsMuted(false);
    }
    try {
      await playerService.setVolume(newVolume);
    } catch (error) {
      console.warn('Error setting volume:', error);
    }
  };

  const toggleMute = async () => {
    if (!isHost) return;
    if (isMuted) {
      setPlayerState(prev => ({ ...prev, volume: previousVolume }));
      setIsMuted(false);
      try {
        await playerService.setVolume(previousVolume);
      } catch (error) {
        console.warn('Error unmuting:', error);
      }
    } else {
      setPreviousVolume(playerState.volume);
      setPlayerState(prev => ({ ...prev, volume: 0 }));
      setIsMuted(true);
      try {
        await playerService.setVolume(0);
      } catch (error) {
        console.warn('Error muting:', error);
      }
    }
  };

  const handleProgressClick = async (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isHost || !playerState.duration) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newPosition = percentage * playerState.duration;

    try {
      await playerService.seek(newPosition);
    } catch (error) {
      console.warn('Error seeking:', error);
    }
  };

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Utiliser currentTrackData si fourni, sinon utiliser playerState.currentTrack
  const displayTrack = currentTrackData || playerState.currentTrack;

  if (!playerState.isReady) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
        <p className="text-gray-400">Connexion au lecteur Spotify...</p>
      </div>
    );
  }

  if (!displayTrack) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 text-center">
        <Music className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-400">Aucune piste sélectionnée</p>
        {isHost && <p className="text-sm text-gray-500 mt-2">Ajoutez une piste pour commencer</p>}
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 shadow-xl border border-gray-700">
      {/* En-tête */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-bold text-white">
          {isHost ? '🎵 Lecteur Spotify' : '🎵 En cours de lecture'}
        </h2>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${playerState.isPlaying ? 'bg-green-500 animate-pulse' : 'bg-gray-500'}`}></div>
          <span className="text-xs text-gray-400">
            {isHost ? 'Contrôle total' : 'Lecture seule'}
          </span>
        </div>
      </div>

      {/* Informations de la piste */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="relative">
          <img
            src={displayTrack.album?.images?.[0]?.url || displayTrack.imageUrl || '/placeholder-album.png'}
            alt={displayTrack.name || displayTrack.title}
            className="w-24 h-24 rounded-lg shadow-lg"
          />
          <div className="absolute inset-0 bg-black bg-opacity-20 rounded-lg"></div>
          {playerState.isPlaying && (
            <div className="absolute bottom-2 right-2 flex space-x-1">
              <div className="w-1 h-3 bg-white rounded animate-pulse"></div>
              <div className="w-1 h-3 bg-white rounded animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-1 h-3 bg-white rounded animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-bold text-xl text-white truncate">
            {displayTrack.name || displayTrack.title}
          </h3>
          <p className="text-gray-300 text-lg truncate">
            {displayTrack.artists?.map((artist: any) => artist.name).join(', ') || displayTrack.artist}
          </p>
          <p className="text-sm text-gray-400 truncate">
            {displayTrack.album?.name || displayTrack.album}
          </p>
        </div>
      </div>

      {/* Barre de progression */}
      <div className="mb-6">
        <div className="flex items-center justify-between text-sm text-gray-300 mb-3">
          <span className="font-mono">{formatTime(playerState.position)}</span>
          <span className="font-mono">{formatTime(playerState.duration)}</span>
        </div>
        <div
          className={`relative w-full bg-gray-700 rounded-full h-3 shadow-inner ${isHost ? 'cursor-pointer' : 'cursor-default'}`}
          onClick={isHost ? handleProgressClick : undefined}
        >
          <div
            className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500 shadow-lg"
            style={{ width: `${playerState.duration > 0 ? (playerState.position / playerState.duration) * 100 : 0}%` }}
          />
          <div
            className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-purple-500 transition-all duration-500 hover:scale-110"
            style={{ left: `${playerState.duration > 0 ? (playerState.position / playerState.duration) * 100 : 0}%`, marginLeft: '-8px' }}
          />
        </div>
      </div>

      {/* Contrôles de lecture - Seulement pour l'hôte */}
      {isHost && showControls && (
        <div className="flex items-center justify-center space-x-6 mb-6">
          <Button
            size="icon"
            variant="ghost"
            onClick={previousTrack}
            className="h-12 w-12 hover:bg-gray-700 transition-colors"
          >
            <SkipBack className="h-6 w-6 text-gray-300" />
          </Button>
          <Button
            size="icon"
            variant="default"
            onClick={togglePlay}
            className="h-16 w-16 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 shadow-lg transition-all duration-200 transform hover:scale-105"
          >
            {playerState.isPlaying ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8 ml-1" />}
          </Button>
          <Button
            size="icon"
            variant="ghost"
            onClick={nextTrack}
            className="h-12 w-12 hover:bg-gray-700 transition-colors"
          >
            <SkipForward className="h-6 w-6 text-gray-300" />
          </Button>
        </div>
      )}

      {/* Indicateur pour les invités */}
      {!isHost && (
        <div className="flex items-center justify-center mb-6">
          <div className="flex items-center space-x-2 bg-gray-700 px-4 py-2 rounded-full">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <p className="text-xs text-purple-400 font-medium">
              Contrôlé par l'hôte
            </p>
          </div>
        </div>
      )}

      {/* Contrôle de volume - Seulement pour l'hôte */}
      {isHost && showControls && (
        <div className="flex items-center space-x-3 mb-6">
          <Button
            size="sm"
            variant="ghost"
            onClick={toggleMute}
            className="p-2 hover:bg-gray-700"
          >
            {isMuted || playerState.volume === 0 ? (
              <VolumeX className="h-5 w-5 text-gray-400" />
            ) : (
              <Volume2 className="h-5 w-5 text-gray-300" />
            )}
          </Button>
          <div className="flex-1 relative">
            <input
              type="range"
              min="0"
              max="100"
              value={playerState.volume}
              onChange={handleVolumeChange}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, #8b5cf6 0%, #8b5cf6 ${playerState.volume}%, #374151 ${playerState.volume}%, #374151 100%)`
              }}
            />
          </div>
          <span className="text-sm text-gray-300 w-12 text-right font-mono">{playerState.volume}%</span>
        </div>
      )}

      {/* Contrôles de vote */}
      {votingEnabled && onVote && (
        <div className="border-t border-gray-600 pt-6">
          <h4 className="text-lg font-bold mb-4 text-center text-white">⭐ Votez pour cette piste</h4>
          <div className="flex items-center justify-center space-x-3">
            {VOTE_EMOJIS.map(({ emoji, value, icon: Icon, color, label, bgColor }) => {
              const isUserVote = userVote?.emoji === emoji;

              return (
                <Button
                  key={emoji}
                  size="lg"
                  variant={isUserVote ? "default" : "ghost"}
                  onClick={() => onVote(emoji, value)}
                  className={`${color} hover:${color} ${bgColor} ${isUserVote ? 'ring-2 ring-white bg-gray-700' : 'hover:bg-gray-700'} transition-all duration-200 transform hover:scale-110`}
                  title={isUserVote ? `Votre vote: ${label} (${value > 0 ? '+' : ''}${value} pts)` : `${label}: ${value > 0 ? '+' : ''}${value} points`}
                >
                  <Icon className="h-5 w-5" />
                </Button>
              );
            })}
          </div>
          {currentTrackData?.score !== undefined && (
            <div className="text-center mt-4">
              <div className="inline-flex items-center space-x-2 bg-gray-700 px-4 py-2 rounded-full">
                <span className="text-sm text-gray-300">Score total:</span>
                <span className="text-xl font-bold text-purple-400">{currentTrackData.score}</span>
                <span className="text-sm text-gray-300">pts</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
