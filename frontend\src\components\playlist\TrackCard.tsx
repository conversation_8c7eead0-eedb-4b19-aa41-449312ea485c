import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { SingleVoteButton } from '@/components/ui/VoteButton';
import { Pin, Trash2, MoreVertical, Clock } from 'lucide-react';
import { VOTE_EMOJIS } from '@/constants/vote-emojis';
import { cn } from '@/lib/utils';

interface TrackCardProps {
  track: any;
  session: any;
  isHost: boolean;
  isGuest: boolean;
  variant: 'mobile' | 'desktop';
  position?: number;
  showPosition?: boolean;
  isPlayed?: boolean;
  onVote?: (trackId: string, emoji: string, value: number) => void;
  onPin?: (trackId: string) => void;
  onRemove?: (trackId: string) => void;
}

export function TrackCard({
  track,
  session,
  isHost,
  isGuest,
  variant,
  position,
  showPosition = false,
  isPlayed = false,
  onVote,
  onPin,
  onRemove
}: TrackCardProps) {
  const [showActions, setShowActions] = useState(false);
  const isMobile = variant === 'mobile';

  // Get user's vote for this track
  const userVote = track.votes?.find(
    (vote: any) => vote.participantId === session?.currentParticipant?.id
  );

  // Check if user can remove this track
  const canRemove = isHost || track.submitter?.id === session?.currentParticipant?.id;

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleVote = (emoji: string, value: number) => {
    onVote?.(track.id, emoji, value);
  };

  return (
    <div className={cn(
      "bg-gray-800/50 rounded-lg border border-gray-700/50",
      "transition-all duration-200 hover:bg-gray-700/50",
      isPlayed && "opacity-60",
      isMobile ? "p-3" : "p-4"
    )}>
      <div className="flex items-center space-x-3">
        {/* Position Number */}
        {showPosition && position && (
          <div className={cn(
            "flex-shrink-0 w-8 h-8 rounded-full bg-purple-600/20 text-purple-400",
            "flex items-center justify-center text-sm font-bold"
          )}>
            {position}
          </div>
        )}

        {/* Album Art */}
        <div className={cn(
          "flex-shrink-0 rounded-md overflow-hidden",
          isMobile ? "w-12 h-12" : "w-16 h-16"
        )}>
          <img
            src={track.imageUrl || '/placeholder-album.png'}
            alt={`${track.title} album art`}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Track Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="min-w-0 flex-1">
              <h4 className={cn(
                "font-semibold text-white truncate",
                isMobile ? "text-sm" : "text-base"
              )}>
                {track.title}
              </h4>
              <p className={cn(
                "text-gray-400 truncate",
                isMobile ? "text-xs" : "text-sm"
              )}>
                {track.artist}
              </p>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-gray-500">
                  {track.submitter?.user?.displayName || track.submitter?.name}
                </span>
                <span className="text-xs text-gray-500">•</span>
                <span className="text-xs text-gray-500">
                  {formatDuration(track.duration)}
                </span>
                {isPlayed && (
                  <>
                    <span className="text-xs text-gray-500">•</span>
                    <Clock className="h-3 w-3 text-gray-500" />
                  </>
                )}
              </div>
            </div>

            {/* Score */}
            <div className="flex-shrink-0 ml-2">
              <span className={cn(
                "font-bold",
                track.score > 0 ? "text-green-400" : 
                track.score < 0 ? "text-red-400" : "text-gray-400"
              )}>
                {track.score > 0 ? '+' : ''}{track.score}
              </span>
            </div>
          </div>

          {/* Voting & Actions */}
          {!isPlayed && (
            <div className={cn(
              "flex items-center justify-between mt-3",
              isMobile && "flex-col space-y-2"
            )}>
              {/* Vote Buttons */}
              {session?.settings?.votingEnabled && (
                <div className="flex items-center space-x-1">
                  {VOTE_EMOJIS.map(({ emoji, value }) => (
                    <SingleVoteButton
                      key={emoji}
                      emoji={emoji}
                      value={value}
                      isSelected={userVote?.emoji === emoji}
                      onClick={() => handleVote(emoji, value)}
                      size={isMobile ? 'sm' : 'md'}
                    />
                  ))}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center space-x-1">
                {/* Pin Button (Host only) */}
                {isHost && onPin && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onPin(track.id)}
                    className="text-gray-400 hover:text-yellow-400 h-8 w-8"
                    title="Pin as next track"
                  >
                    <Pin className="h-4 w-4" />
                  </Button>
                )}

                {/* Remove Button */}
                {canRemove && onRemove && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onRemove(track.id)}
                    className="text-gray-400 hover:text-red-400 h-8 w-8"
                    title="Remove track"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
