import React from 'react';
import { LiveStats } from '@/components/stats/LiveStats';
import { Leaderboard } from '@/components/stats/Leaderboard';
import { TopTracks } from '@/components/stats/TopTracks';
import { useDeviceDetection } from '@/hooks/useDeviceDetection';
import { BarChart3, Trophy, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatisticsTabProps {
  session: any;
  isHost: boolean;
  isGuest: boolean;
}

export function StatisticsTab({ session, isHost, isGuest }: StatisticsTabProps) {
  const { isMobile } = useDeviceDetection();

  if (isMobile) {
    return (
      <div className="h-full bg-gray-900 overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-gray-900/95 backdrop-blur-lg border-b border-gray-700 p-4 z-10">
          <div className="flex items-center space-x-3">
            <BarChart3 className="h-6 w-6 text-purple-400" />
            <h1 className="text-2xl font-bold text-white">Statistics</h1>
          </div>
        </div>

        {/* Mobile Layout - Stacked */}
        <div className="p-4 space-y-6">
          {/* Live Stats */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="h-5 w-5 text-green-400" />
              <h2 className="text-lg font-semibold text-white">Live Stats</h2>
            </div>
            <LiveStats session={session} variant="mobile" />
          </div>

          {/* Leaderboard */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Trophy className="h-5 w-5 text-yellow-400" />
              <h2 className="text-lg font-semibold text-white">Leaderboard</h2>
            </div>
            <Leaderboard session={session} variant="mobile" />
          </div>

          {/* Top Tracks */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <BarChart3 className="h-5 w-5 text-blue-400" />
              <h2 className="text-lg font-semibold text-white">Top Tracks</h2>
            </div>
            <TopTracks session={session} variant="mobile" />
          </div>
        </div>
      </div>
    );
  }

  // Desktop Layout - Grid
  return (
    <div className="h-full bg-gray-900 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-3 mb-2">
          <BarChart3 className="h-8 w-8 text-purple-400" />
          <h1 className="text-3xl font-bold text-white">Session Statistics</h1>
        </div>
        <p className="text-gray-400">
          Real-time insights and analytics for your session
        </p>
      </div>

      {/* Desktop Grid Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 h-full">
        {/* Live Stats */}
        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center space-x-2 mb-4">
            <TrendingUp className="h-6 w-6 text-green-400" />
            <h2 className="text-xl font-semibold text-white">Live Stats</h2>
          </div>
          <LiveStats session={session} variant="desktop" />
        </div>

        {/* Leaderboard */}
        <div className="bg-gray-800 rounded-xl p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Trophy className="h-6 w-6 text-yellow-400" />
            <h2 className="text-xl font-semibold text-white">Leaderboard</h2>
          </div>
          <Leaderboard session={session} variant="desktop" />
        </div>

        {/* Top Tracks */}
        <div className="bg-gray-800 rounded-xl p-6 lg:col-span-2 xl:col-span-1">
          <div className="flex items-center space-x-2 mb-4">
            <BarChart3 className="h-6 w-6 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Top Tracks</h2>
          </div>
          <TopTracks session={session} variant="desktop" />
        </div>
      </div>
    </div>
  );
}
