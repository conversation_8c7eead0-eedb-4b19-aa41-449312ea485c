import React, { useState, useEffect } from 'react';
import { Music, Users, TrendingUp, Star, Flame, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LiveStatsProps {
  session: any;
  variant: 'mobile' | 'desktop';
}

interface SessionStats {
  totalTracks: number;
  totalVotes: number;
  totalParticipants: number;
  averageScore: number;
  playedTracks: number;
  sessionDuration: number;
  topContributor: string;
  mostVotedTrack: string;
}

export function LiveStats({ session, variant }: LiveStatsProps) {
  const [stats, setStats] = useState<SessionStats>({
    totalTracks: 0,
    totalVotes: 0,
    totalParticipants: 0,
    averageScore: 0,
    playedTracks: 0,
    sessionDuration: 0,
    topContributor: '',
    mostVotedTrack: '',
  });

  useEffect(() => {
    if (!session) return;

    const tracks = session.tracks || [];
    const participants = session.participants || [];

    // Calculate basic stats
    const totalTracks = tracks.length;
    const totalVotes = tracks.reduce((sum: number, track: any) => 
      sum + (track.votes?.length || 0), 0
    );
    const totalParticipants = participants.length;
    const playedTracks = tracks.filter((track: any) => track.hasPlayed).length;
    
    // Calculate average score
    const totalScore = tracks.reduce((sum: number, track: any) => sum + track.score, 0);
    const averageScore = totalTracks > 0 ? totalScore / totalTracks : 0;

    // Calculate session duration
    const sessionStart = new Date(session.createdAt);
    const sessionDuration = Math.floor((Date.now() - sessionStart.getTime()) / 1000 / 60); // in minutes

    // Find top contributor
    const contributorCounts = tracks.reduce((acc: Record<string, number>, track: any) => {
      const contributor = track.submitter?.user?.displayName || track.submitter?.name || 'Unknown';
      acc[contributor] = (acc[contributor] || 0) + 1;
      return acc;
    }, {});

    const topContributor = Object.entries(contributorCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))[0]?.[0] || 'None';

    // Find most voted track
    const mostVotedTrack = tracks
      .sort((a: any, b: any) => (b.votes?.length || 0) - (a.votes?.length || 0))[0]?.title || 'None';

    setStats({
      totalTracks,
      totalVotes,
      totalParticipants,
      averageScore: Math.round(averageScore * 10) / 10,
      playedTracks,
      sessionDuration,
      topContributor,
      mostVotedTrack,
    });
  }, [session]);

  const isMobile = variant === 'mobile';

  const statItems = [
    {
      label: 'Total Tracks',
      value: stats.totalTracks,
      icon: Music,
      color: 'text-blue-400',
      bgColor: 'bg-blue-400/10',
    },
    {
      label: 'Participants',
      value: stats.totalParticipants,
      icon: Users,
      color: 'text-green-400',
      bgColor: 'bg-green-400/10',
    },
    {
      label: 'Total Votes',
      value: stats.totalVotes,
      icon: Flame,
      color: 'text-orange-400',
      bgColor: 'bg-orange-400/10',
    },
    {
      label: 'Played',
      value: stats.playedTracks,
      icon: Star,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-400/10',
    },
    {
      label: 'Avg Score',
      value: stats.averageScore,
      icon: TrendingUp,
      color: 'text-purple-400',
      bgColor: 'bg-purple-400/10',
    },
    {
      label: 'Duration',
      value: `${stats.sessionDuration}m`,
      icon: Clock,
      color: 'text-gray-400',
      bgColor: 'bg-gray-400/10',
    },
  ];

  return (
    <div className="space-y-4">
      {/* Stats Grid */}
      <div className={cn(
        "grid gap-3",
        isMobile ? "grid-cols-2" : "grid-cols-3"
      )}>
        {statItems.map((item) => {
          const Icon = item.icon;
          return (
            <div
              key={item.label}
              className={cn(
                "rounded-lg p-3 border border-gray-700/50",
                item.bgColor
              )}
            >
              <div className="flex items-center justify-between mb-2">
                <span className={cn("text-xs text-gray-400", isMobile && "text-xs")}>
                  {item.label}
                </span>
                <Icon className={cn("h-4 w-4", item.color)} />
              </div>
              <p className={cn(
                "font-bold text-white",
                isMobile ? "text-lg" : "text-xl"
              )}>
                {item.value}
              </p>
            </div>
          );
        })}
      </div>

      {/* Additional Info */}
      <div className="space-y-3">
        {stats.topContributor !== 'None' && (
          <div className="bg-purple-900/20 border border-purple-700/30 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Top Contributor</span>
              <TrendingUp className="h-4 w-4 text-purple-400" />
            </div>
            <p className="font-semibold text-purple-400 truncate">
              {stats.topContributor}
            </p>
          </div>
        )}

        {stats.mostVotedTrack !== 'None' && (
          <div className="bg-orange-900/20 border border-orange-700/30 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-400">Most Voted Track</span>
              <Flame className="h-4 w-4 text-orange-400" />
            </div>
            <p className="font-semibold text-orange-400 truncate">
              {stats.mostVotedTrack}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
