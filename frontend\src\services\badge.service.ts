import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  points: number;
  criteria: {
    type: string;
    threshold: number;
  };
  unlockedAt?: Date;
}

export interface UserStats {
  userId: string;
  totalTracks: number;
  totalVotes: number;
  totalPoints: number;
  sessionsHosted: number;
  sessionsJoined: number;
  tracksPlayed: number;
  badges: Badge[];
}

export class BadgeService {
  static async getUserStats(userId?: string): Promise<UserStats> {
    const url = userId ? `/playlist/stats/${userId}` : '/playlist/stats';
    const response = await axios.get(`${API_URL}${url}`);
    return response.data;
  }

  static async checkBadges(): Promise<{ newBadges: Badge[] }> {
    const response = await axios.post(`${API_URL}/playlist/badges/check`);
    return response.data;
  }

  static async getLeaderboard(): Promise<Array<{
    userId: string;
    name: string;
    points: number;
    badges: Badge[];
    rank: number;
  }>> {
    const response = await axios.get(`${API_URL}/playlist/leaderboard`);
    return response.data;
  }

  // Définition des badges disponibles
  static readonly BADGES = {
    FIRST_TRACK: {
      name: 'Premier pas',
      description: 'Ajouter votre première piste',
      icon: '🎵',
      criteria: { type: 'tracks_added', threshold: 1 },
    },
    TRACK_MASTER: {
      name: 'Maître des pistes',
      description: 'Ajouter 10 pistes',
      icon: '🎼',
      criteria: { type: 'tracks_added', threshold: 10 },
    },
    VOTE_ENTHUSIAST: {
      name: 'Voteur enthousiaste',
      description: 'Voter 25 fois',
      icon: '🗳️',
      criteria: { type: 'votes_cast', threshold: 25 },
    },
    POPULAR_TRACK: {
      name: 'Piste populaire',
      description: 'Une de vos pistes atteint +10 votes',
      icon: '⭐',
      criteria: { type: 'track_score', threshold: 10 },
    },
    SESSION_HOST: {
      name: 'Hôte expérimenté',
      description: 'Organiser 5 sessions',
      icon: '👑',
      criteria: { type: 'sessions_hosted', threshold: 5 },
    },
    SOCIAL_BUTTERFLY: {
      name: 'Papillon social',
      description: 'Rejoindre 10 sessions',
      icon: '🦋',
      criteria: { type: 'sessions_joined', threshold: 10 },
    },
    POINT_COLLECTOR: {
      name: 'Collectionneur de points',
      description: 'Atteindre 100 points',
      icon: '💎',
      criteria: { type: 'total_points', threshold: 100 },
    },
    MUSIC_GURU: {
      name: 'Gourou musical',
      description: 'Atteindre 500 points',
      icon: '🧙‍♂️',
      criteria: { type: 'total_points', threshold: 500 },
    },
    STREAK_MASTER: {
      name: 'Maître des séries',
      description: '5 pistes consécutives avec score positif',
      icon: '🔥',
      criteria: { type: 'positive_streak', threshold: 5 },
    },
    EARLY_BIRD: {
      name: 'Lève-tôt',
      description: 'Première personne à rejoindre une session',
      icon: '🐦',
      criteria: { type: 'first_to_join', threshold: 1 },
    },
  };

  // Vérifier les badges localement (pour feedback immédiat)
  static checkLocalBadges(userStats: UserStats, sessionStats: any): Badge[] {
    const newBadges: Badge[] = [];
    const currentBadgeIds = userStats.badges.map(b => b.id);

    Object.entries(this.BADGES).forEach(([key, badge]) => {
      if (currentBadgeIds.includes(key)) return; // Badge déjà débloqué

      let shouldUnlock = false;

      switch (badge.criteria.type) {
        case 'tracks_added':
          shouldUnlock = userStats.totalTracks >= badge.criteria.threshold;
          break;
        case 'votes_cast':
          shouldUnlock = userStats.totalVotes >= badge.criteria.threshold;
          break;
        case 'sessions_hosted':
          shouldUnlock = userStats.sessionsHosted >= badge.criteria.threshold;
          break;
        case 'sessions_joined':
          shouldUnlock = userStats.sessionsJoined >= badge.criteria.threshold;
          break;
        case 'total_points':
          shouldUnlock = userStats.totalPoints >= badge.criteria.threshold;
          break;
        case 'track_score':
          // Vérifier si une piste de l'utilisateur a atteint le score requis
          shouldUnlock = sessionStats?.userTracks?.some((track: any) => 
            track.score >= badge.criteria.threshold
          ) || false;
          break;
        case 'positive_streak':
          // Vérifier la série de pistes avec score positif
          shouldUnlock = sessionStats?.positiveStreak >= badge.criteria.threshold || false;
          break;
        case 'first_to_join':
          shouldUnlock = sessionStats?.isFirstToJoin || false;
          break;
      }

      if (shouldUnlock) {
        newBadges.push({
          id: key,
          name: badge.name,
          description: badge.description,
          icon: badge.icon,
          points: 50, // Points par défaut pour débloquer un badge
          criteria: badge.criteria,
          unlockedAt: new Date(),
        });
      }
    });

    return newBadges;
  }

  // Calculer les statistiques de session pour un utilisateur
  static calculateSessionStats(session: any, userId: string) {
    // Filtrer les tracks en vérifiant que user n'est pas null
    const userTracks = session.tracks.filter((track: any) =>
      track.submitter?.user?.id === userId
    );

    // Calculer la série de pistes avec score positif
    let positiveStreak = 0;
    let currentStreak = 0;

    userTracks.forEach((track: any) => {
      if (track.score > 0) {
        currentStreak++;
        positiveStreak = Math.max(positiveStreak, currentStreak);
      } else {
        currentStreak = 0;
      }
    });

    // Vérifier si c'est le premier à rejoindre (en vérifiant que user n'est pas null)
    const isFirstToJoin = session.participants.length > 0 &&
      session.participants[0]?.user?.id === userId;

    return {
      userTracks,
      positiveStreak,
      isFirstToJoin,
    };
  }
}
