import { Flame, Heart, Star, ThumbsDown, Frown } from 'lucide-react';

export const VOTE_EMOJIS = [
  { emoji: '🔥', value: 3, icon: Flame, color: 'text-orange-500', label: 'Excellent', bgColor: 'hover:bg-orange-500/10' },
  { emoji: '❤️', value: 2, icon: Heart, color: 'text-red-500', label: 'J\'adore', bgColor: 'hover:bg-red-500/10' },
  { emoji: '⭐', value: 1, icon: Star, color: 'text-yellow-500', label: 'J\'aime', bgColor: 'hover:bg-yellow-500/10' },
  { emoji: '👎', value: -1, icon: ThumbsDown, color: 'text-gray-500', label: 'Bof', bgColor: 'hover:bg-gray-500/10' },
  { emoji: '😞', value: -2, icon: Frown, color: 'text-red-400', label: 'N\'aime pas', bgColor: 'hover:bg-red-400/10' },
];

export const getVoteEmojiConfig = (emoji: string) => {
  return VOTE_EMOJIS.find(config => config.emoji === emoji);
};

export const isValidVoteValue = (value: number): boolean => {
  return value >= -2 && value <= 3;
};
