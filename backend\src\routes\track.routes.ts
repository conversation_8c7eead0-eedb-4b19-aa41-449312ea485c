import { Router } from 'express';
import { TrackController } from '../controllers/track.controller';
import { authMiddleware, guestAuthMiddleware } from '../middleware/auth.middleware';

const router = Router();

// Unified routes (allow both authenticated users and guests)
router.get('/search', guestAuthMiddleware, TrackController.searchTracksForGuest);
router.post('/sessions/:sessionCode/tracks', guestAuthMiddleware, TrackController.addTrackForGuest);
router.post('/sessions/:sessionCode/tracks/:trackId/vote', guestAuthMiddleware, TrackController.voteTrackForGuest);

// Legacy guest routes (for backward compatibility)
router.get('/search-guest', guestAuthMiddleware, TrackController.searchTracksForGuest);
router.post('/sessions/:sessionCode/tracks-guest', guestAuthMiddleware, TrackController.addTrackForGuest);
router.post('/sessions/:sessionCode/tracks/:trackId/vote-guest', guestAuthMiddleware, TrackController.voteTrackForGuest);

// Host-only routes (require authentication)
router.use(authMiddleware);
router.delete('/sessions/:sessionCode/tracks/:trackId', TrackController.removeTrack);
router.put('/sessions/:sessionCode/tracks/:trackId/playing', TrackController.updatePlayingTrack);

export default router;
