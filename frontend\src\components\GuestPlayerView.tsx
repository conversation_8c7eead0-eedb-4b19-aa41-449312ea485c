import { useState, useEffect } from 'react';
import { Music } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { VOTE_EMOJIS } from '@/constants/vote-emojis';

interface GuestPlayerViewProps {
  currentTrackData?: any;
  onVote?: (emoji: string, value: number) => void;
  userVote?: { emoji: string; value: number } | null;
  votingEnabled?: boolean;
}

export function GuestPlayerView({
  currentTrackData,
  onVote,
  userVote,
  votingEnabled = false
}: GuestPlayerViewProps) {
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(0);

  // Simuler la progression de la timeline (approximative)
  useEffect(() => {
    if (!currentTrackData) return;

    setDuration(currentTrackData.duration || 0);
    
    // Simuler la progression (approximative)
    const interval = setInterval(() => {
      setPosition(prev => {
        const newPos = prev + 1000; // +1 seconde
        return newPos >= duration ? 0 : newPos;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [currentTrackData, duration]);

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!currentTrackData) {
    return (
      <div className="bg-gray-800 rounded-lg p-6 text-center">
        <p className="text-gray-400">Aucune piste en cours de lecture</p>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-6 shadow-xl border border-gray-700">
      {/* En-tête */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-bold text-white">🎵 En cours de lecture</h2>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-xs text-green-400">Live</span>
        </div>
      </div>

      {/* Informations de la piste */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="relative">
          {currentTrackData.imageUrl ? (
            <img
              src={currentTrackData.imageUrl}
              alt={currentTrackData.title}
              className="w-24 h-24 rounded-lg shadow-lg"
            />
          ) : (
            <div className="w-24 h-24 bg-gray-600 rounded-lg flex items-center justify-center">
              <Music className="h-12 w-12 text-gray-400" />
            </div>
          )}
          <div className="absolute inset-0 bg-black bg-opacity-20 rounded-lg"></div>
          {/* Indicateur de lecture */}
          <div className="absolute bottom-2 right-2 flex space-x-1">
            <div className="w-1 h-3 bg-white rounded animate-pulse"></div>
            <div className="w-1 h-3 bg-white rounded animate-pulse" style={{animationDelay: '0.2s'}}></div>
            <div className="w-1 h-3 bg-white rounded animate-pulse" style={{animationDelay: '0.4s'}}></div>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-bold text-xl text-white truncate">{currentTrackData.title}</h3>
          <p className="text-gray-300 text-lg truncate">{currentTrackData.artist}</p>
          <p className="text-sm text-gray-400 truncate">{currentTrackData.album}</p>
          <div className="flex items-center space-x-2 mt-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <p className="text-xs text-purple-400 font-medium">
              Contrôlé par l'hôte
            </p>
          </div>
        </div>
      </div>

      {/* Timeline approximative */}
      <div className="mb-6">
        <div className="flex items-center justify-between text-sm text-gray-300 mb-3">
          <span className="font-mono">{formatTime(position)}</span>
          <span className="text-xs text-gray-500">Timeline approximative</span>
          <span className="font-mono">{formatTime(duration)}</span>
        </div>
        <div className="relative w-full bg-gray-700 rounded-full h-3 shadow-inner">
          <div
            className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-1000 shadow-lg"
            style={{ width: `${duration > 0 ? (position / duration) * 100 : 0}%` }}
          />
          <div
            className="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-white rounded-full shadow-lg border-2 border-purple-500 transition-all duration-1000"
            style={{ left: `${duration > 0 ? (position / duration) * 100 : 0}%`, marginLeft: '-8px' }}
          />
        </div>
      </div>

      {/* Contrôles de vote pour participants */}
      {votingEnabled && onVote && (
        <div className="border-t border-gray-600 pt-6">
          <h4 className="text-lg font-bold mb-4 text-center text-white">⭐ Votez pour cette piste</h4>
          <div className="flex items-center justify-center space-x-3">
            {VOTE_EMOJIS.map(({ emoji, value, icon: Icon, color, label }) => {
              const isUserVote = userVote?.emoji === emoji;

              return (
                <Button
                  key={emoji}
                  size="lg"
                  variant={isUserVote ? "default" : "ghost"}
                  onClick={() => onVote(emoji, value)}
                  className={`${color} hover:${color} ${isUserVote ? 'ring-2 ring-white bg-gray-700' : 'hover:bg-gray-700'} transition-all duration-200 transform hover:scale-110`}
                  title={isUserVote ? `Votre vote: ${label} (${value > 0 ? '+' : ''}${value} pts)` : `${label}: ${value > 0 ? '+' : ''}${value} points`}
                >
                  <Icon className="h-5 w-5" />
                </Button>
              );
            })}
          </div>
          <div className="text-center mt-4">
            <div className="inline-flex items-center space-x-2 bg-gray-700 px-4 py-2 rounded-full">
              <span className="text-sm text-gray-300">Score total:</span>
              <span className="text-xl font-bold text-purple-400">{currentTrackData.score}</span>
              <span className="text-sm text-gray-300">pts</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
